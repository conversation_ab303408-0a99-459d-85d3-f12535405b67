/* eslint-disable max-depth */

/**
 * 对话数据控制器 Hook
 *
 * 职责：管理医生端IM对话的完整生命周期，包括：
 * - SSE（Server-Sent Events）实例管理和生命周期控制
 * - 用户消息和AI回复的创建、更新、状态管理
 * - 实时数据流处理和UI更新协调
 * - 错误处理和重连机制
 * - 性能监控和用户行为统计
 *
 * 核心概念：
 * - SSE实例：处理实时AI对话的长连接
 * - 消息队列：管理对话中的消息顺序和状态
 * - 思考消息：AI处理过程中的loading状态展示
 * - 胶囊工具：AI推荐的交互式组件
 */
import {useCallback, useRef} from 'react';
import {genImMsgKey} from '@baidu/vita-utils-shared';
import {useAtom, useAtomValue, useSetAtom} from 'jotai';

import {
    createResendMsgArgAtom,
    endTriageStreamMsgAtom,
    updateTriageStreamMsgAtom,
    createTriageStreamMsgAtom,
    updateCurSessionMsgIdsAtom,
    getDocImMsgDataByKey
} from '../../store/docImAtom/msg';
import {
    lastMsgIdAtom,
    curSessionIdAtom,
    getTransDataAtom,
    updateDataForUbcAtom,
    lastConversionMsgIdAtom,
    sessionCapsulesToolsAtom,
    sessionCapsulesToolsMd5Atom,
    renderSessionCapsulesAtom
} from '../../store/docImAtom';
import {getAgreementInfoAtom, resetAgreementInfoAtom} from '../../store/docImAtom/otherData';

import {useGetUrlParams} from '../common';
import {useScrollControl} from '../common/useScrollControl';

import {
    MSG_CARDID_ENUM,
    MSG_CARDID_ENUM_STREAM,
    MSG_CARDID_ENUM_STREAM_TYPE,
    type MSG_CARDID_TYPE
} from '../../constants/msg';

import {getUseractionReq} from '../../models/services/docIm';
import {conversationSSE, type SSEProcessorInstance} from '../../models/services/docIm/sse';

import {ubcCommonViewSend} from '../../utils/generalFunction/ubc';

import type {
    MsgItemType,
    CapsulesToolsType,
    SSEResponseType
} from '../../store/docImAtom/index.type';
import type {MsgId} from '../../typings';
import type {AgreementDataType} from '../../models/services/docIm/index.d';

import {useGetInputData} from './pageDataController';
import {
    convertStreamMsgProtocol,
    updateSpecialCardAdjectiveMsgId,
    convertSystemMsg
} from './msgUtils';

import type {CreateConversationArgs} from './index.d';

/**
 * SSE实例管理队列 - 全局状态
 *
 * 用于跟踪所有活跃的SSE连接，每个实例包含：
 * - id: 唯一标识符，用于实例管理和清理
 * - thinkingMsgKey: 关联的"思考中"消息ID
 * - relatedMsgIds: 该SSE会话产生的所有消息ID列表
 * - instance: SSE连接实例，用于数据接收和连接控制
 *
 * 注意：这是一个全局队列，需要careful管理避免内存泄漏
 */
const curSSEInstance: {
    id: symbol;
    thinkingMsgKey: MsgId;
    relatedMsgIds: MsgId[];
    instance: SSEProcessorInstance<SSEResponseType>;
}[] = [];

/**
 * SSE主动中断状态记录
 *
 * 用于区分SSE连接是用户主动中断还是网络错误：
 * - true: 用户主动点击停止按钮
 * - false/undefined: 网络错误或服务端异常
 *
 * 小程序环境下无法自动检测中断类型，需要手动标记
 */
let isActivelyAborted: {
    [key: symbol]: boolean;
} = {};

/**
 * 获取页面 Conversation 交互数据
 *
 * @param _id 会话ID
 * @returns 返回一个控制器，用于控制页面 Conversation 交互数据
 */
export const useConversationDataController = () => {
    // === 内部状态管理 ===

    /** 当前会话最后一条消息ID的本地备份 */
    const lastMsgId = useRef<MsgId>('');

    /** SSE连接是否成功建立的标记 */
    const isSSEConnectionSuccessful = useRef(false);

    /** SSE数据流是否处理完成的标记 */
    const isSSEDataProcessingComplete = useRef(false);

    /** 是否已收到首个LLM token的标记（用于性能统计） */
    const hasReceivedFirstToken = useRef(false);

    const sessionId = useAtomValue(curSessionIdAtom);
    const updateLastMsgId = useSetAtom(lastMsgIdAtom);
    const setSessionCapsulesTools = useSetAtom(sessionCapsulesToolsAtom);
    const [lastConversionMsgId, setLastConversionMsgId] = useAtom(lastConversionMsgIdAtom);
    const [sessionCapsulesToolsMd5, setSessionCapsulesToolsMd5] = useAtom(
        sessionCapsulesToolsMd5Atom
    );
    const setRenderSessionCapsules = useSetAtom(renderSessionCapsulesAtom);
    const {scrollToBottom} = useScrollControl('docImScrollControl');

    const {inputData} = useGetInputData();
    const {expertId, expert_id} = useGetUrlParams();

    /**
     * 创建并展示用户消息
     *
     * 功能说明：
     * 1. 将用户输入转换为标准消息协议格式
     * 2. 将消息添加到当前会话的消息队列中
     * 3. 更新消息状态和UI显示
     * 4. 更新会话的最后消息ID（用于后续引用）
     *
     * 副作用：
     * - 调用 updateCurSessionMsgIdsAtom 更新消息队列
     * - 调用 createTriageStreamMsgAtom 创建消息原子
     * - 调用 updateLastMsgId 更新最后消息ID
     * - 调用 updateDataForUbcAtom 更新UBC统计数据
     *
     * @param msg 用户输入的消息内容和类型
     * @param msgKey 消息的唯一标识符
     * @returns 创建的消息对象，失败时返回 undefined
     */
    const mockUserMsg = useCallback(
        (msg: CreateConversationArgs['msg'], msgKey: string): MsgItemType<unknown> | undefined => {
            const ext = {
                sessionId,
                msgKey
            };

            let msgDataProtocol: MsgItemType<unknown> | undefined;
            const msgCardIdMap: {
                [k in (typeof msg)['type']]: MSG_CARDID_TYPE;
            } = {
                text: MSG_CARDID_ENUM.ImText
            };
            if (!sessionId) return undefined;

            msgCardIdMap[msg.type] &&
                (msgDataProtocol = convertStreamMsgProtocol({
                    ext: {
                        sessionId: ext?.sessionId || '',
                        msgKey: ext?.msgKey
                    },
                    cardId: msgCardIdMap[msg.type],
                    content: {value: msg.content}
                }));
            if (msgDataProtocol) {
                updateCurSessionMsgIdsAtom([msgKey], {
                    type: 'push'
                });

                createTriageStreamMsgAtom(`${sessionId}_${msgKey}`, msgDataProtocol);

                updateLastMsgId(msgKey);
                updateDataForUbcAtom({
                    lastMsgId: msgKey
                });
                lastMsgId.current = msgKey;
            }

            return msgDataProtocol;
        },
        [sessionId, updateLastMsgId]
    );

    const mockAgreementSysMsg = useCallback(
        ({msgKey, agreementInfo}: {msgKey: MsgId; agreementInfo: AgreementDataType}) => {
            try {
                if (!sessionId) return;

                const sysMsgKey = `mock_sys_${msgKey}`;
                const sysMsgData = convertSystemMsg({
                    content: agreementInfo,
                    sessionId,
                    msgKey
                });

                updateCurSessionMsgIdsAtom([sysMsgKey], {
                    type: 'push'
                });

                createTriageStreamMsgAtom(`${sessionId}_${sysMsgKey}`, {
                    ...sysMsgData,
                    meta: {
                        ...sysMsgData.meta,
                        localExt: {
                            dataSource: 'mock',
                            insertType: 'push',
                            needScrollToBottom: true
                        }
                    }
                });

                resetAgreementInfoAtom();
            } catch (err) {
                console.error('mockAgreementSysMsg 失败：', err);
            }
        },
        [sessionId]
    );

    /**
     * 创建并展示"思考中"加载消息
     *
     * 功能说明：
     * 1. 在AI处理用户请求期间显示加载状态
     * 2. 使用配置中的loadingTips文案（可自定义）
     * 3. 标记为本地mock数据，自动滚动到底部
     * 4. 当AI开始返回数据时，该消息会被自动移除
     *
     * 副作用：同 mockUserMsg，但数据来源标记为 'mock'
     *
     * @param msgKey 思考消息的唯一标识符
     * @returns 思考消息的msgKey，用于后续删除操作
     */
    const addThinkMsg = useCallback(
        (msgKey: MsgId): string => {
            if (!sessionId) return '';

            const ext = {
                sessionId,
                msgKey
            };

            const data = convertStreamMsgProtocol({
                ext: {
                    sessionId: ext?.sessionId || '',
                    msgKey: ext?.msgKey
                },
                cardId: 2,
                content: {value: inputData?.loadingTips || ''}
            });

            if (data) {
                updateCurSessionMsgIdsAtom([msgKey], {
                    type: 'push'
                });

                createTriageStreamMsgAtom(`${sessionId}_${msgKey}`, {
                    ...data,
                    meta: {
                        ...data.meta,
                        localExt: {
                            dataSource: 'mock',
                            insertType: 'push',
                            needScrollToBottom: true
                        }
                    }
                });
                updateLastMsgId(msgKey);
                updateDataForUbcAtom({
                    lastMsgId: msgKey
                });

                lastMsgId.current = msgKey;
            }

            return msgKey;
        },
        [inputData?.loadingTips, sessionId, updateLastMsgId]
    );

    // 更新消息数据；
    const updateMsgData = useCallback(
        (msgItemList: MsgItemType<unknown>[], {sseId}: {sseId: symbol}) => {
            if (!sessionId) return;

            // 只在首 token 时记录渲染时间
            const isFirstToken = !hasReceivedFirstToken.current;
            const renderStartTime = isFirstToken ? new Date().getTime() : 0;

            msgItemList?.length &&
                msgItemList.forEach(msg => {
                    const {meta, data} = msg;
                    msg.meta.localExt = {
                        ...msg.meta.localExt,
                        dataSource: 'conversation',
                        needScrollToBottom: true
                    };

                    updateCurSessionMsgIdsAtom([meta.msgId], {
                        type: 'push'
                    });
                    updateTriageStreamMsgAtom(`${sessionId}_${meta.msgId}`, msg);

                    // 更新有效定向SKU消息的ID；
                    if (
                        (data?.content?.cardId as MSG_CARDID_ENUM_STREAM_TYPE) ===
                        MSG_CARDID_ENUM_STREAM.ImCollectedInfoAndSku
                    ) {
                        updateSpecialCardAdjectiveMsgId(
                            data?.content?.cardId as MSG_CARDID_ENUM_STREAM_TYPE,
                            meta.msgId,
                            {type: 'push'}
                        );
                    }

                    // 更新当前SSE实例关联的消息ID列表，便于后续关闭SSE时清理相关消息数据；
                    curSSEInstance.some(i => {
                        if (sseId === i.id) {
                            !i.relatedMsgIds.includes(meta.msgId) &&
                                i.relatedMsgIds.push(meta.msgId);

                            return true;
                        }

                        return false;
                    });
                });

            // 更新会话的最后一条消息 ID；
            if (msgItemList?.length) {
                const metaData = msgItemList[msgItemList.length - 1].meta;
                updateLastMsgId(metaData.msgId);
                setLastConversionMsgId(metaData.msgId);
                lastMsgId.current = metaData.msgId;

                updateDataForUbcAtom({
                    lastMsgId: metaData.msgId,
                    rounds: metaData.rounds
                });
            }

            // 只在首 token 时计算并上报渲染时间
            if (isFirstToken) {
                const renderEndTime = new Date().getTime();
                const renderTime = renderEndTime - renderStartTime + 20;
                ubcCommonViewSend({
                    value: 'tokenFirstTimeRender',
                    ext: {
                        product_info: {
                            costTime: renderTime
                        }
                    }
                });
            }
        },
        [sessionId, updateLastMsgId, setLastConversionMsgId]
    );

    // 更新本次 sse 实例关联消息的数据；
    const updateRelatedMsgData = useCallback(
        id => {
            if (!sessionId) return;
            let instance: (typeof curSSEInstance)[number] | undefined;

            curSSEInstance.some(i => {
                if (id === i.id) {
                    instance = i;

                    return true;
                }
            });

            instance?.relatedMsgIds.forEach(msgId => {
                endTriageStreamMsgAtom(`${sessionId}_${msgId}`);
            });
        },
        [sessionId]
    );

    const setActivelyAborted = useCallback(
        (args: {id: symbol; status: 'aborted'; _debugSymbol?: string}) => {
            isActivelyAborted = {
                ...isActivelyAborted,
                [args.id]: args.status === 'aborted'
            };
        },
        []
    );

    // 取消上一个 SSE 实例；
    const cancelPreSSE = useCallback(
        (reason: string) => {
            if (!sessionId) return;

            const preSEEInstance = curSSEInstance.pop();
            if (preSEEInstance?.instance) {
                preSEEInstance?.instance?.close?.();

                // Tips：小程序无法分辨是否为主动打断，所以需要手动设置；@wanghaoyu08
                if (process.env.TARO_ENV !== 'h5') {
                    setActivelyAborted({
                        id: preSEEInstance.id,
                        status: 'aborted'
                    });
                }

                updateCurSessionMsgIdsAtom([preSEEInstance?.thinkingMsgKey], {
                    type: 'delete'
                });

                sessionId &&
                    preSEEInstance.relatedMsgIds.forEach(msgId => {
                        endTriageStreamMsgAtom(`${sessionId}_${msgId}`);
                    });

                const eventId = preSEEInstance?.instance?.getCurrentEventId();

                getUseractionReq<'stopConv'>({
                    bizActionType: 'stopConv',
                    chatData: {
                        sessionId,
                        expertId: Number(expertId || expert_id)
                    },
                    bizActionData: {
                        stopConvInfo: {
                            reason,
                            eventId: String(eventId),
                            msgId: lastConversionMsgId
                        }
                    }
                });
            }
        },
        [sessionId, expertId, expert_id, lastConversionMsgId, setActivelyAborted]
    );

    // SSE 实例完成回调；
    const onSSEComplete = useCallback(
        (arg: {
            thinkingMsgMsgId: MsgId;
            withOutThinkingMsg: boolean;
            sseId: SSEProcessorInstance<SSEResponseType>['id'];
        }) => {
            try {
                const {sseId, thinkingMsgMsgId, withOutThinkingMsg} = arg;

                !withOutThinkingMsg &&
                    updateCurSessionMsgIdsAtom([thinkingMsgMsgId], {
                        type: 'delete'
                    });
                updateRelatedMsgData(sseId);

                const index = curSSEInstance.findIndex(item => item.id === sseId);

                if (index !== -1) {
                    curSSEInstance.splice(index, 1);
                }
            } catch (err) {
                console.error('conversationSSE onComplete 出错：', err);
            }
        },
        [updateRelatedMsgData]
    );

    // sse 建立成功回调；
    const onSEEConnect = useCallback(
        (arg: {msgContent: MsgItemType<unknown> | undefined; msgKey: string}) => {
            const {msgContent, msgKey} = arg;

            if (!msgContent || !sessionId) return;

            const updatedMsg: MsgItemType<unknown> = {
                ...msgContent,
                meta: {
                    ...msgContent?.meta,
                    localMsgStatus: 'success'
                }
            };

            updateTriageStreamMsgAtom(`${sessionId}_${msgKey}`, updatedMsg);
        },
        [sessionId]
    );

    // SSE 实例出错回调；
    const onSEEError = useCallback(
        (arg: {
            msgKey: string;
            resendArg: CreateConversationArgs;
            sseId: SSEProcessorInstance<SSEResponseType>['id'];
            errorContent: {status?: string} | unknown;
            lastReplyId: MsgId;
            msgContent: MsgItemType<unknown> | undefined;
        }) => {
            const {msgContent, msgKey, sseId, resendArg, lastReplyId} = arg;

            if (!msgContent || !sessionId) return;

            const isAborted =
                process.env.TARO_ENV === 'h5'
                    ? (arg.errorContent as {status?: string})?.status === 'aborted'
                    : isActivelyAborted[sseId];

            if (!isAborted) {
                createResendMsgArgAtom(msgKey, resendArg);
            }

            const lastReplyMsgData = getDocImMsgDataByKey(`${sessionId}_${lastReplyId}`);

            if (lastReplyId !== '' && lastReplyMsgData) {
                const updatedMsg = {
                    ...lastReplyMsgData,
                    meta: {
                        ...lastReplyMsgData?.meta,
                        localMsgStatus: 'aborted' as const
                    }
                };

                updateTriageStreamMsgAtom(`${sessionId}_${lastReplyId}`, updatedMsg, {
                    _debugSymbol: 'onSEEError',
                    actionType: 'update'
                });
            }

            if (msgContent) {
                const updatedMsg = {
                    ...msgContent,
                    meta: {
                        ...msgContent?.meta,
                        localMsgStatus:
                            lastReplyId !== '' && lastReplyMsgData
                                ? ('aborted' as const)
                                : ('rejected' as const)
                    }
                };
                updateTriageStreamMsgAtom(`${sessionId}_${msgKey}`, updatedMsg, {
                    _debugSymbol: 'onSEEError'
                });
            }

            // TODO: sse-kit 增加判断是打断还是错误；@wanghaoyu08
            // const updatedMsg = {
            //     ...msgContent,
            //     ...{
            //         ...msgContent?.meta,
            //         localMsgStatus: 'success'
            //     }
            // };
            // updateTriageStreamMsgAtom(`${sessionId}_${msgKey}`, updatedMsg);
        },
        [sessionId]
    );

    // 更新会话胶囊工具；
    const updateSessionCapsulesTools = useCallback(
        (arg: {md5: string; list: CapsulesToolsType[]}) => {
            if (sessionCapsulesToolsMd5 !== arg.md5) {
                setSessionCapsulesToolsMd5(arg.md5);

                setSessionCapsulesTools(arg.list);
            }
        },
        [sessionCapsulesToolsMd5, setSessionCapsulesTools, setSessionCapsulesToolsMd5]
    );

    /**
     * 创建新的AI对话会话（核心功能）
     *
     * 完整流程：
     * 【阶段1 - 准备工作】
     *   - 生成消息key和思考消息key
     *   - 取消前一个SSE连接（避免并发冲突）
     *   - 创建用户消息并显示在界面上
     *
     * 【阶段2 - 建立SSE连接】
     *   - 初始化性能计时器
     *   - 配置三大核心回调：onComplete/onHeadersReceived/onError
     *   - 发起conversationSSE请求
     *
     * 【阶段3 - 注册SSE实例】
     *   - 将SSE实例添加到全局管理队列
     *   - 绑定关联的消息ID列表
     *
     * 【阶段4 - 处理实时数据流】
     *   - 循环处理SSE数据块（for await异步迭代）
     *   - 处理胶囊工具数据更新
     *   - 处理AI消息数据更新
     *   - 处理首token性能统计
     *   - 触发完成回调
     *
     * 性能监控：
     * - 首token返回时间（tokenFirstTimeApi）
     * - 首token渲染时间（tokenFirstTimeRender）
     *
     * 错误处理：
     * - 全局try-catch捕获异常
     * - 各个阶段的特定错误由对应回调处理
     *
     * @param arg 对话创建参数
     * @param arg.msg 用户消息内容和类型
     * @param arg.withOutMsg 是否跳过创建用户消息（默认false）
     * @param arg.withOutThinkingMsg 是否跳过创建思考消息（默认false）
     * @param arg.ctrlData 控制数据（首次调用时需要）
     * @param arg.transData 传输数据（额外参数）
     */
    const createConversation = useCallback(
        async (arg: CreateConversationArgs) => {
            try {
                // === 阶段1：参数解构和准备工作 ===
                const {
                    msg, // 用户消息内容
                    withOutMsg = false, // 是否跳过用户消息创建
                    withOutThinkingMsg = false, // 是否跳过思考消息创建
                    ctrlData, // 控制数据（首次调用所需）
                    transData // 额外传输参数
                } = arg;

                // 生成本次对话的唯一标识符
                const msgKey = genImMsgKey(10);
                const thinkingMsgMsgId = `thinking_${msgKey}`;

                // 取消前一个SSE连接，避免并发冲突
                cancelPreSSE('用户新建会话主动停止');

                // 创建用户消息并展示在界面上
                let mockUserMsgContent: MsgItemType<unknown> | undefined;
                if (!withOutMsg) {
                    mockUserMsgContent = mockUserMsg(msg, msgKey);
                    // 消息假上墙之后滚动到底部
                    scrollToBottom('user_send_msg_to_wall');
                }

                // === 阶段2：初始化性能计时器和建立SSE连接 ===

                // 重置首token标记（用于性能监控）
                hasReceivedFirstToken.current = false;

                // 记录SSE请求开始时间
                const startSSETime = new Date().getTime();
                let firstResponseTime: number;

                // 处理透传参数，会在 core 和 getMsg 接口中更新数据；
                const transDataForSend = transData || getTransDataAtom() || undefined;

                // 用于处理『服务声明』假上墙相关逻辑；
                const agreement = getAgreementInfoAtom();
                if (!ctrlData?.firstCall && agreement) {
                    mockAgreementSysMsg({
                        msgKey,
                        agreementInfo: agreement
                    });
                }

                // 建立SSE连接并配置三大核心回调
                const r = await conversationSSE({
                    chatData: {
                        sessionId,
                        sceneType: msg.sceneType || 'unknown'
                    },
                    ...(transDataForSend ? {transData: transDataForSend} : {}),
                    msg: {payload: [{msgKey, content: msg.content}]},
                    ...(ctrlData?.firstCall ? {ctrlData} : {}),
                    // SSE连接成功完成回调
                    onComplete: () => {
                        isSSEConnectionSuccessful.current = true;

                        // 只有当连接成功且数据处理完成时，才触发最终完成回调
                        isSSEConnectionSuccessful.current &&
                            isSSEDataProcessingComplete.current &&
                            onSSEComplete({
                                sseId: r.id,
                                thinkingMsgMsgId,
                                withOutThinkingMsg
                            });
                    },
                    // SSE连接建立成功回调（收到HTTP响应头）
                    onHeadersReceived: () => {
                        // 更新用户消息为成功状态
                        onSEEConnect({
                            msgKey,
                            msgContent: mockUserMsgContent
                        });

                        // 展示“思考中”加载消息
                        !withOutThinkingMsg && addThinkMsg(thinkingMsgMsgId);

                        // 重置首token标记（备用）
                        hasReceivedFirstToken.current = false;
                    },
                    // SSE连接错误回调（网络错误、服务端错误、用户中断）
                    onError: error => {
                        onSEEError({
                            msgKey,
                            resendArg: arg, // 保存重发参数
                            msgContent: mockUserMsgContent,
                            errorContent: error,
                            sseId: r.id,
                            lastReplyId: lastMsgId.current
                        });
                        // eslint-disable-next-line no-console
                        console.info('conversationSSE onError 出错：', error);
                    }
                });

                // === 阶段3：注册SSE实例到全局管理器 ===

                const sseId = r.id;
                curSSEInstance.push({
                    id: sseId, // SSE实例的唯一标识符
                    instance: r, // SSE连接实例
                    relatedMsgIds: [], // 该对话产生的所有消息ID
                    thinkingMsgKey: thinkingMsgMsgId // 关联的思考消息ID
                });

                // === 阶段4：处理SSE实时数据流 ===

                if (r) {
                    // 循环处理SSE数据流（异步迭代器）
                    for await (const chunk of r.message()) {
                        // 只处理成功状态的数据块（status: 0）
                        if (chunk?.status === 0) {
                            // 处理胶囊工具数据更新
                            if (chunk?.data?.toolData?.capsules) {
                                updateSessionCapsulesTools(chunk?.data?.toolData?.capsules);
                                setRenderSessionCapsules(true);
                            }

                            // 处理AI消息数据更新
                            if (chunk?.data?.message) {
                                updateMsgData(chunk?.data?.message, {sseId});
                                // 移除“思考中”加载消息
                                updateCurSessionMsgIdsAtom([thinkingMsgMsgId], {
                                    type: 'delete'
                                });
                            }

                            // 处理首token性能统计（仅在首次收到token时）
                            if (
                                !hasReceivedFirstToken.current &&
                                chunk?.data?.ctrlData?.firstToken
                            ) {
                                hasReceivedFirstToken.current = true;

                                // 记录首token返回时间
                                firstResponseTime = new Date().getTime();

                                // 计算并上报API响应时间
                                if (startSSETime) {
                                    const firstTokenTime = firstResponseTime - startSSETime;
                                    ubcCommonViewSend({
                                        value: 'tokenFirstTimeApi',
                                        ext: {
                                            product_info: {
                                                costTime: firstTokenTime,
                                                msgId: chunk?.data?.message?.[0]?.meta?.msgId || '',
                                                talkId:
                                                    chunk?.data?.message?.[0]?.data?.content?.data
                                                        ?.ext?.talkId || ''
                                            }
                                        }
                                    });
                                }
                            }
                        }
                    }
                    // === 数据流处理完成，触发最终完成回调 ===

                    isSSEDataProcessingComplete.current = true;

                    // 双重检查：连接成功 + 数据处理完成
                    isSSEConnectionSuccessful.current &&
                        isSSEDataProcessingComplete.current &&
                        onSSEComplete({
                            sseId: r.id,
                            thinkingMsgMsgId,
                            withOutThinkingMsg
                        });
                }
            } catch (err) {
                console.error('createConversation 出错：', err);
                return;
            }
        },
        [
            sessionId,
            onSEEError,
            mockUserMsg,
            addThinkMsg,
            onSEEConnect,
            cancelPreSSE,
            onSSEComplete,
            updateMsgData,
            scrollToBottom,
            mockAgreementSysMsg,
            setRenderSessionCapsules,
            updateSessionCapsulesTools
        ]
    );

    return {
        mockUserMsg,
        cancelPreSSE,
        createConversation
    };
};

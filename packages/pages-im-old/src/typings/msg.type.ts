import {
    MSG_CARDID_ENUM,
    MSG_CARDID_ENUM_STREAM,
    type MSG_CARDID_TYPE,
    type MSG_CARDID_STREAM_TYPE
} from '../constants/msg';

import type {MsgId} from './index.d';

// export enum InteractionEnum {
//     '请求' = 'request',
//     '打开链接' = 'openLink',
//     '底部弹窗' = 'popup',
//     '拉起支付' = 'payment',
//     '关闭' = 'close',
//     '全局 modal' = 'modal',
//     '跳过 ai 问题' = 'skipAi',
//     '发送ubc日志' = 'sendUbc',
//     '重新加载' = 'reload',
//     '打开pdf' = 'openPdf',
//     '打开第三方小程序' = 'openOtherApp',
//     '打开小程序' = 'openMiniApp',
//     '请求后打开微信小程序' = 'requestWexinJump',
//     '拨打电话' = 'callPhone',
//     '异步请求' = 'asyncRequest',
//     'toast弹层' = 'toast',
//     '渲染物料数据' = 'renderPlanMaterial',
//     'ai历史记录弹窗' = 'historyMsgPop'
// }
export type SessionId = string;

export type InteractionType = string;

export interface InteractionInfo {
    intent?: string;
    url?: string;
    sceneType?: string;
    method?: 'GET' | 'POST';
    contentType?: string;
    wxPayConfig?: unknown;
    wxPayInfo?: {[key: string]: string};
    inlinePaySign?: string;
    type?: string;
    version?: string;
    params?: {
        [key in string]: string | number | unknown;
    };
    fail?: () => void;
    success?: () => void;
}

export interface MsgItemType<T> {
    type: 'dynamic' | 'static';
    meta: MsgMetaData;
    data: MsgInstanceData<T>;
}

export interface MsgMetaData {
    msgId: MsgId;
    rounds?: number;
    sendTime: string;
    createTime: string;
    sessionId: SessionId;
    posterInfo: PosterInfo;
    ownerType: OwnerTypeValue;
    showPosterRole: PosterRoleType;
    localMsgStatus?: 'pending' | 'rejected' | 'success' | 'aborted'; // 用于处理本地消息发送状态，非持久化字段；
    localExt?: {
        // 消息数据来源；mock 为本地 mock 数据，conversation 为 conversation sse 返回数据，history 为获取历史消息数据；
        dataSource: 'conversation' | 'history' | 'mock';
        insertType?: 'unshift' | 'push';
        needScrollToBottom?: boolean;
        localMsgStatus?: 'pending' | 'rejected' | 'success'; // 用于处理本地消息发送状态，非持久化字段；
    };
}

export interface MsgInstanceData<T> {
    action: 'append' | 'end';
    content: ICardProps<T>; // T 为具体的消息卡片协议；
    searchReferences?: SearchReferences;
    feature?: {
        attributeStatus: AttributeStatusValue;
        // 功能数据；
        like: {
            icon: {
                imgUrl: string;
                selectedImgUrl: string;
            };
            actionInfo: InteractionInfo;
        };
        dislike: {
            icon: {
                imgUrl: string;
                selectedImgUrl: string;
            };
            actionInfo: InteractionInfo;
        };
        tts: {
            // 语音播报相关数据
            speaker: string;
        };
        copyInfo: {
            // 复制功能
            enable: boolean;
        };
        ext: [];
    };
}

export interface SearchReferences {
    title: string;
    content: string;
    isFinish: boolean;
}

export const AttributeStatus = {
    UNSELECT: 0,
    LIKE: 1,
    DISLIKE: 2
} as const;

export type AttributeStatusValue = (typeof AttributeStatus)[keyof typeof AttributeStatus];
export type AttributeStatusKey = keyof typeof AttributeStatus;

export const PosterRole = {
    RoleUser: 1,
    RoleDoctor: 2,
    RoleAssistantDoctor: 3,
    RoleRobot: 4,
    RoleBusiness: 5,
    RoleZhiGuDoctor: 6,
    RoleDocAgent: 7
} as const;

export type PosterRoleType = (typeof PosterRole)[keyof typeof PosterRole];

// ------------------ Card 相关 ------------------
export interface ICardProps<T> {
    cardId: MSG_CARDID_TYPE | MSG_CARDID_STREAM_TYPE;
    cardName: keyof typeof MSG_CARDID_ENUM | keyof typeof MSG_CARDID_ENUM_STREAM;
    data: CardsData<T>;
    version?: number;
    ubc?: {
        value: string;
        production_info: {
            [k in string]: string | number;
        };
    };
}

export interface CardsData<T> {
    cardStyle?: CardStyle;
    actionInfo?: ActionInfo;
    content: T;
    ext?: {
        [key: string]: string | number;
    };
}

export interface CardStyle {
    needHead?: boolean;
    renderType?: number;
    isHidden?: boolean;
    oneByOne?: boolean; // InfoCollectorForm 特殊非通用字段
}

export interface ActionInfo {
    interaction: InteractionType;
    interactionInfo: InteractionInfo;
}

export interface DispatchEventCallbackParams {
    interaction: InteractionType;
    interactionInfo: InteractionInfo;
}
// ------------------ Card 相关 ------------------

// ------------------ Message 相关 ------------------
// eslint-disable-next-line no-shadow
export enum OwnerTypeEnum {
    '系统' = 1,
    '服务方' = 2,
    '需求方' = 3
}
export type OwnerTypeValue = `${Extract<OwnerTypeEnum, number>}` extends `${infer N extends number}`
    ? N
    : never;

export interface PosterInfo {
    name?: string;
    avatar?: string;
    title?: string;
    link?: string;
    disableClickOut?: boolean; // 点击消息头像是否可以跳转到医生主页 | 我的问医生
}

import {useState, useCallback, useRef, useEffect} from 'react';

/**
 * useState Hooks 提供同步获取setState最新值的方法 getState
 * 使用： const [val, setVal, getVal] = useGetState(0);
 * @param initVal 初始值
 * @returns
 */
export function useGetState<T>(initVal: T): [T, (newVal: T) => Promise<T>, () => T] {
    const [state, setState] = useState<T>(initVal);
    const ref = useRef(initVal);
    const setStateCopy = (newVal: T): Promise<T> => {
        return new Promise(resolve => {
            setState(newVal);
            ref.current = newVal;
            resolve(newVal);
        });
    };
    const getState = () => ref.current;

    return [state, setStateCopy, getState];
}

/**
 * 使用setTimeout的自定义Hook
 *
 * @param callback 延迟执行的回调函数
 * @param delay 延迟时间，单位为毫秒
 * @returns 返回一个函数，调用该函数可启动或停止setTimeout
 */
export function useTimeout<T extends(...args: any[]) => any>(callback: T, delay: number) {
    const timeoutId = useRef<ReturnType<typeof setTimeout>>();
    const savedCallback = useRef(callback);
    const isTimeoutActive = useRef(false);
    const isMounted = useRef(true);

    // 保持回调最新
    useEffect(() => {
        savedCallback.current = callback;
        if (isTimeoutActive.current) {
            setisTimeoutActive(true);
        }
    }, [callback]);

    // 追踪组件挂载状态
    useEffect(() => {
        return () => {
            isMounted.current = false;
        };
    }, []);

    const setisTimeoutActive = useCallback(
        (active: boolean) => {
            if (!isMounted.current) return;

            if (timeoutId.current) {
                clearTimeout(timeoutId.current);
            }

            isTimeoutActive.current = active;

            if (active) {
                timeoutId.current = setTimeout(() => {
                    if (isMounted.current) {
                        savedCallback.current();
                    }
                }, delay);
            }
        },
        [delay]
    );

    return setisTimeoutActive;
}

//初始化按钮组件
import cx from 'classnames';
import {debounce} from 'lodash-es';
import {View} from '@tarojs/components';
import {HButton} from '@baidu/health-ui';
import {eventCenter} from '@tarojs/taro';
import {memo, type FC, useCallback, useEffect} from 'react';

import {PUBLIC_SEND_MSG_EVENT} from '../../../../pages-im/src/constants/msg';
import {
    ubcCommonViewSend,
    ubcCommonClkSend
} from '../../../../pages-im/src/utils/generalFunction/ubc';

import MarkDown from '../MarkDown';

import type {MarkDownBtnProps} from './index.d';
import styles from './index.module.less';

const MarkDownBtn: FC<MarkDownBtnProps> = ({markdownBtn, msgId, ext, page}) => {
    const {actionInfo, description} = markdownBtn || {};
    const {interaction, interactionInfo, value} = actionInfo || {};

    // 曝光点位
    useEffect(() => {
        ubcCommonViewSend({
            value: 'im_flow_markdown_btn_view',
            ext: {
                product_info: {
                    msgId,
                    ...ext
                }
            }
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    /**
     * @description 问诊消息发送
     */
    const updateWzMessage = useCallback(
        async (interactionInfo, ops) => {
            try {
                const {params = {}, sceneType = ''} = interactionInfo;
                const {payload = []} = params || {payload: []};
                let contentStr = '';
                (payload as Array<unknown>).forEach(item => {
                    if ((item as Record<string, string>)?.content) {
                        contentStr += (item as Record<string, string>)?.content;
                    }
                });

                if (contentStr) {
                    eventCenter.trigger(PUBLIC_SEND_MSG_EVENT, {
                        args: {
                            msg: {
                                type: 'text',
                                content: contentStr,
                                sceneType: sceneType || ops?.sceneType
                            }
                        },
                        _symbol: 'MarkDownBtn',
                        sourcePageType: page === 'im' ? 'im' : 'docIm'
                    });
                }
            } catch (error) {
                console.error(error);
            }
        },
        [page]
    );

    // 发送消息
    const handleClickSendMsg = useCallback(() => {
        if (interaction === 'sendMsg') {
            updateWzMessage?.(interactionInfo, {
                sceneType: 'imFlowOptions'
            });
            ubcCommonClkSend({
                value: 'im_flow_markdown_btn_click',
                ext: {
                    product_info: {
                        msgId,
                        ...ext
                    }
                }
            });
        }
    }, [ext, interaction, interactionInfo, msgId, updateWzMessage]);

    return (
        <View className={styles.markdownBtn}>
            <MarkDown content={description} msgEnd={true} />
            <HButton
                onClick={debounce(() => handleClickSendMsg(), 500)}
                className={cx(styles.markdownBtnBtn, 'c-click-status')}
                text={value}
                size={48}
            />
        </View>
    );
};
MarkDownBtn.displayName = 'MarkDownBtn';
export default memo(MarkDownBtn);

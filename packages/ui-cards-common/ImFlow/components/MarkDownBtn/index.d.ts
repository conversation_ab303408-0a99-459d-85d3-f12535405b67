import type {InteractionType, InteractionInfo} from '@baidu/vita-pages-im';
export interface MarkDownBtnProps {
    markdownBtn: {
        description: string;
        actionInfo: {
            interaction: InteractionType;
            interactionInfo: InteractionInfo;
            value: string;
        };
    };
    msgId?: string;
    page: string;
    ext?: {
        [key: string]: string | number;
    };
}

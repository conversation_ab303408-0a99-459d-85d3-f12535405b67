/* stylelint-disable */
.markdown {
    font-size: 51px;
    padding: 42px 0 6px;
    line-height: 87px;
    letter-spacing: 1.5px;
    font-family: PingFang SC;
    word-break: break-all;
    overflow: hidden;

    .fragment {
        display: inline;
    }

    .p {
        padding: 0;
        margin: 0;
    }

    .strong,
    .em {
        color: #1f1f1f;
        font-style: normal;
        font-weight: bold;
    }

    .a {
        color: #00bdbd;
        display: inline-block;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        font-size: 51px;
        font-weight: bold;
        line-height: normal;
    }

    .ol,
    .ul {
        padding-left: 60px;

        .li {
            margin-bottom: 9px;
            display: flex;
            align-items: flex-start;
            font-family: PingFang SC;

            .ol-marker,
            .ul-marker {
                margin-right: 10px;
                min-width: 20px;
            }

            .li-content {
                flex: 1;
            }
        }
    }

    .ol {
        counter-reset: list-number;
        position: relative;
        padding: 0;

        .li {
            list-style-type: none;
            counter-increment: list-number;

            &::marker {
                display: none;
            }

            .ol-marker {
                content: counter(list-number) + '.';
            }
        }
    }

    .ul {
        list-style-type: circle;
        padding-left: 0px;

        .li {
            list-style-type: none;
        }
    }

    .image-container {
        .markdown-image {
            width: 100%;
        }
    }

    .code-block {
        margin: 20px 0;
        padding: 12px;
        background-color: #f6f8fa;
        border-radius: 4px;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;

        .code-content {
            font-family: monospace;
            font-size: 51px;
            line-height: 1.5;
            white-space: pre-wrap;
        }
    }

    .inline-code {
        font-family: monospace;
        background-color: #f6f8fa;
        padding: 2px 4px;
        border-radius: 4px;
        font-size: 51px;
    }

    .table {
        margin: 20px 0;
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        border-radius: 4px;
        box-shadow: 0 0 0 1px #ddd;

        .table-content {
            min-width: 100%;
            display: inline-block;
        }

        .table-row {
            display: flex;
            border-bottom: 1px solid #ddd;

            &:last-child {
                border-bottom: none;
            }

            &.table-header {
                background-color: #f5f5f5;
            }
        }

        .table-cell {
            flex: 1;
            padding: 8px 12px;
            border-right: 1px solid #ddd;
            min-width: 300px;

            &:last-child {
                border-right: none;
            }
        }
    }

    .bold {
        font-weight: bold;
        display: inline;
    }

    .italic {
        font-style: italic;
        display: inline;
    }

    .strikethrough {
        text-decoration: line-through;
        display: inline;
    }

    .hr {
        height: 1px;
        background-color: #ddd;
        margin: 20px 0;
    }

    .blockquote {
        border-left: 4px solid #ddd;
        padding-left: 16px;
        margin: 16px 0;
        color: #848691;
        font-size: 45px;
    }

    .html-content {
        margin: 16px 0;
        padding: 12px;
        background-color: #f6f8fa;
        border-radius: 4px;
        font-family: monospace;
    }

    .br {
        height: 1px;
        margin: 8px 0;
    }

    .definition {
        margin: 16px 0;
        padding: 12px;
        background-color: #f6f8fa;
        border-radius: 4px;

        .definition-term {
            font-weight: bold;
            margin-bottom: 8px;
        }

        .definition-description {
            padding-left: 16px;
            border-left: 2px solid #ddd;
        }
    }

    .element-block {
        margin-bottom: 36px;
    }

    .loading-icon {
        display: inline-block;
        width: 30px;
        margin-left: 24px;
        aspect-ratio: 1;
        border-radius: 50%;
        background: rgba(#30a34e, 0.4);
        opacity: 0.4;
        animation: loadAnimation 1s linear infinite;
        animation-direction: reverse;
    }

    .gradient-icon {
        position: relative;
    }

    .gradient-icon::after {
        content: '';
        position: absolute;
        z-index: 5;
        left: 0;
        top: 0;
        display: inline-block;
        width: 300px;
        height: 60px;
        background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 100%);
        transform: translate3d(-100%, 5px, 0);
    }
}

@keyframes loadAnimation {
    0% {
        transform: scale(1);
        background-color: #00c8c8;
        opacity: 0.4;
    }

    50% {
        transform: scale(1.7);
        background-color: #00c8c8;
        opacity: 0.15;
    }

    100% {
        transform: scale(1);
        background-color: #00c8c8;
        opacity: 0.4;
    }
}

import type {ReactNode} from 'react';
import type {ImFlowProps, CardComponentType} from '../../index.d';

export interface MarkDownCardProps {
    // 用户类型，用于区分卡片样式
    type?: 'doctor' | 'patient';
    msgData?: ImFlowProps;
    isLatest?: boolean;
    children?: ReactNode;
    className?: string;
    showToolbar?: boolean;
    cardType?: CardComponentType;
    isInterrupted?: boolean;
    isQuickSkuReply?: boolean;
    header?: ReactNode;
    // 基于页面区分
    page?: 'docIm' | 'im';
}

/**
 * @file markdown解析组件
 * @author: <EMAIL>
 */
import {type FC, memo, useEffect, useMemo, useRef, useState} from 'react';
import {View} from '@tarojs/components';

import MarkDownAst from '../MarkDownAst';

import {MarkDdownProps} from './index.d';

const MarkDownCard: FC<MarkDdownProps> = props => {
    const {
        content = '',
        isTypewriter = false,
        speed = 20,
        stepWordNum = 1,
        isFinish,
        loadingType = ':gradient:',
        forcedShowLoading = false,
        isShowLoading = true,
        isMockTypwer = false,
        msgEnd,
        typewriterSuccessCallback,
        typewriterProcessCallback,
        typewriterStartCallback,
        markdownClassName
    } = props;

    const [codeNum, setCodeNum] = useState(0);
    const timerRef = useRef<ReturnType<typeof setTimeout> | null>(null);
    const startedRef = useRef(false);
    const finishedRef = useRef(false);
    const memoLoading = useMemo(() => {
        return isShowLoading ? loadingType : '';
    }, [isShowLoading, loadingType]);

    // 打字机逻辑适配 SSE 场景
    useEffect(() => {
        // 如果不是打字机模式，直接显示全部内容
        if (!isTypewriter || isFinish || msgEnd) {
            setCodeNum(content.length);
            finishedRef.current = true;
            if (timerRef.current) clearTimeout(timerRef.current);
            if (isTypewriter && typewriterSuccessCallback) {
                typewriterSuccessCallback();
            }
            return;
        }
        // content 变短时同步 codeNum
        if (codeNum > content.length) {
            setCodeNum(content.length);
            return;
        }
        // codeNum 追上 content.length 时，停止打字机
        if (codeNum >= content.length) {
            if (!finishedRef.current) {
                finishedRef.current = true;
                if (typewriterSuccessCallback) {
                    typewriterSuccessCallback();
                }
            }
            if (timerRef.current) clearTimeout(timerRef.current);
            return;
        }
        // 启动打字机
        if (!startedRef.current && typewriterStartCallback) {
            typewriterStartCallback();
            startedRef.current = true;
        }
        if (timerRef.current) clearTimeout(timerRef.current);
        timerRef.current = setTimeout(() => {
            setCodeNum(prev => {
                const next = Math.min(prev + stepWordNum, content.length);
                if (typewriterProcessCallback) {
                    typewriterProcessCallback(next);
                }
                return next;
            });
        }, speed);
        return () => {
            if (timerRef.current) clearTimeout(timerRef.current);
        };
    }, [
        stepWordNum,
        content,
        codeNum,
        isTypewriter,
        speed,
        isFinish,
        msgEnd,
        typewriterStartCallback,
        typewriterSuccessCallback,
        typewriterProcessCallback
    ]);

    // 卸载时执行的清理，防止极端情况下的泄漏
    useEffect(() => {
        return () => {
            if (timerRef.current) clearTimeout(timerRef.current);
        };
    }, []);

    const memoTypeHtml = useMemo(() => {
        const _currentContent = content.slice(0, codeNum) || '';

        // 不管打字机是否结束，强制显示 loading
        if (forcedShowLoading) {
            return `${_currentContent}${memoLoading}`;
        }

        // sse输入内容，卡片状态结束，显示全部内容
        if (msgEnd || isFinish) {
            return content;
        }

        // 模拟打字机非sse输入内容，打字机结束，显示全部内容
        if (isMockTypwer && codeNum >= content?.length) {
            return content;
        }
        return `${_currentContent}${memoLoading}`;
    }, [codeNum, content, forcedShowLoading, isFinish, isMockTypwer, memoLoading, msgEnd]);

    const memoRenderKey = useMemo(() => {
        if (process.env.TARO_ENV === 'swan') {
            return memoTypeHtml?.length;
        }
        return 'markdown';
    }, [memoTypeHtml]);

    const markdownHtml = useMemo(() => {
        return <MarkDownAst className={markdownClassName} content={memoTypeHtml} />;
    }, [memoTypeHtml, markdownClassName]);

    // 渲染markdown，容器加 ref 以便自动滚动
    return (
        <View key={memoRenderKey} className='markdownContainer wz-fs-51'>
            {markdownHtml}
        </View>
    );
};

export default memo(MarkDownCard);

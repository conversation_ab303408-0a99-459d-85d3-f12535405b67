import type {
    MsgInstanceData,
    InteractionType,
    InteractionInfo
} from '../../pages-im/src/typings/msg.type';

export interface FeedbackPopupDataProps {
    type: string;
    content: FeedbackPopupData;
}

interface Input {
    placeholder: string;
    value: string;
}

interface Item {
    value: string;
}

interface Content {
    items: Item[];
    subTitle: string;
}

interface FeedbackPopupData {
    input: Input;
    title: string;
    content: Content[];
    submitBtn: {
        value: string;
        interaction: InteractionType;
        interactionInfo: InteractionInfo;
    };
}

export interface ActionInfo {
    value: string;
    interaction: InteractionType;
    interactionInfo: InteractionInfo;
}

export interface CardsData<T> {
    cardStyle?: CardStyle;
    actionInfo?: ActionInfo;
    content: T;
    ext?: {
        [key: string]: string | number;
    };
}

export interface SearchReferences {
    title: string;
    content: string;
    isFinish: boolean;
}

export type Action = 'init' | 'append' | 'end';

export interface ImFlowProps {
    data: MsgInstanceData<ImFlowData>;
    msgId: string;
    lastMsgId: string;
    sessionId: string;
    isLocalInterrupted?: boolean;
    isLatest?: boolean;
    localExt: {
        dataSource: 'conversation' | 'history' | 'mock';
        insertType?: 'unshift' | 'push';
        needScrollToBottom?: boolean;
    };
    // 基于页面区分
    page?: 'docIm' | 'im';
}

interface QuickReply {
    content: string;
}

interface RhetoricalQuestion {
    question: string;
    options: {content: string}[];
}
interface TextList {
    sectionId: string;
    content: string;
    isFinish: boolean;
}

interface MediaInfo {
    images?: {
        icon?: string;
        origin?: string;
        small?: string;
        hasMask?: boolean;
        maskInfo?: {
            text?: string;
        };
    }[];
    videos?: {
        origin?: string;
        thumb?: string;
        width?: number;
        height?: number;
    }[];
}
export interface ImFlowData {
    list: {
        sectionId: string;
        content: string;
        isFinish: boolean;
        planIcon?: string;
        planProgressDesc?: string;
        plan?: {
            id: string;
            name: string;
            status: 'doing' | 'completed';
            description: string;
        }[];
        markdownBtn?: {
            description: string;
            actionInfo: ActionInfo;
        };
        media?: MediaInfo;
        tabList?: {
            header: {
                title: string;
                content: string; // md 格式
            }[];
            body: {
                title: string;
                list: {
                    title: string;
                    content: string; // md 格式
                }[];
                media: MediaInfo;
            }[];
            tabTitle: string;
            content?: string; // 保持一致 markdown 格式；
        };
    }[];
    quickReply?: QuickReply[];
    rhetoricalQuestions?: RhetoricalQuestion[]; // 反问模块
    isInterrupted?: boolean;
    sourceInfo?: {
        text: string;
    };
}

export interface CardStyle {
    [key: string]: string | number;
}

export enum CardComponent {
    'markdown' = 'MarkDown',
    'markdownBtn' = 'MarkDownBtn',
    'searchReferences' = 'SearchReferencesCard'
}

export type CardComponentType = `${CardComponent}`;

import cx from 'classnames';
import {Image, Text, View} from '@tarojs/components';
import {memo, useCallback, useMemo, type FC, type ReactNode, useRef, useState} from 'react';
import {useHeightObserver} from '../../pages-im/src/hooks/common/useHeightObserver';

import type {ImFlowProps, FeedbackPopupDataProps, ImFlowData} from './index.d';
import QuickReply from './components/QuickReply';
import styles from './index.module.less';
import MarkDownCard from './components/MarkDownCard/index';
import MarkDown from './components/MarkDown';
import SearchReferencesCard from './components/SearchReferences';
import MarkDownBtn from './components/MarkDownBtn';
import MarkdownPlan from './components/MarkdownPlan';
import MarkDownImages from './components/MarkDownImages';
import MarkDownVideos from './components/MarkDownVideos';
import MarkdownTabList from './components/MarkdownTabList';
import ThinkingChain from './components/ThinkingChainV2';
import RhetoricalQuestions from './components/RhetoricalQuestions';

const RenderComponent = ({data, children}: {data: unknown; children: ReactNode}) => {
    return data ? children : null;
};

const ImFlow: FC<ImFlowProps> = (msgData: ImFlowProps) => {
    const {
        data,
        msgId,
        isLocalInterrupted,
        localExt,
        page = 'im',
        isLatest,
        lastMsgId
    } = msgData || {};
    const {action, searchReferences} = data || {};
    const {
        list,
        quickReply = [],
        rhetoricalQuestions = [],
        isInterrupted
    } = data?.content?.data?.content || {};
    const interrupted = isLocalInterrupted || isInterrupted;
    const {dataSource} = localExt || {};
    const {renderType} = data?.content?.data?.cardStyle || {};
    // 服务端新增打点拓展字段 和cardStyle平级
    const {ext = {}} = data?.content?.data || {};
    const lastMarkdownIndex = useRef(0);

    const [tabListSummaryEnd, setTabListSummaryEnd] = useState(true);

    // 记录当前正在打印的 tabList 数量
    const [printingTabListCount, setPrintingTabListCount] = useState(0);

    const imFlowId = useMemo(
        () => `im-flow-${msgData.msgId || Math.random().toString(36).substring(2)}`,
        [msgData.msgId]
    );

    // 是否需要滚动到最新消息标识由数据层处理；
    const needScrollToBottom = useMemo(() => {
        return msgData?.localExt?.needScrollToBottom;
    }, [msgData?.localExt?.needScrollToBottom]);

    const isRenderType2 = useMemo(() => renderType === 2, [renderType]);

    const memoIsLastMsg = useMemo(() => {
        return lastMsgId === msgId && action === 'end';
    }, [action, lastMsgId, msgId]);

    const tabListSummaryCondition = useMemo(() => {
        // 如果当前有正在打印的 tabList，则不渲染
        if (printingTabListCount > 0) {
            return false;
        }
        return true;
    }, [printingTabListCount]);

    const quickReplyRenderCondition = useMemo(() => {
        // 如果当前有正在打印的 tabList，则不渲染
        if (isRenderType2) {
            return tabListSummaryEnd;
        }
        return true;
    }, [isRenderType2, tabListSummaryEnd]);

    // 初始化高度变化监听hook
    useHeightObserver(
        imFlowId,
        needScrollToBottom,
        'dom_height_observer',
        page === 'im' ? 'triageStreamScrollControl' : 'docImScrollControl'
    );

    // 渲染 Markdown 卡片头部
    const renderMarkDownCardHeader = useCallback(
        ({title, icon}) => {
            if (isRenderType2) {
                return (
                    <View className={styles.markdownCardHeader}>
                        <View className={styles.markdownCardHeaderIcon}>
                            <Image
                                className={styles.markdownCardHeaderIconImg}
                                src={icon}
                                mode='aspectFit'
                            />
                        </View>
                        <Text className={styles.markdownCardHeaderTitle}>{title}</Text>
                    </View>
                );
            }
            return null;
        },
        [isRenderType2]
    );

    // 渲染卡片组件
    const renderMsgComponents = useCallback(
        list => {
            return list?.map((_item, _index) => {
                const msgEnd = action === 'end';
                const {
                    plan,
                    content,
                    markdownBtn,
                    media,
                    isFinish = false,
                    tabList,
                    planProgressDesc,
                    planIcon
                } = _item || {};
                const {images, videos} = media || {};
                if (content) {
                    lastMarkdownIndex.current = _index;
                }

                const isQuickSkuReply = markdownBtn ? true : false;

                // 如果打断消息，且不是快速回复，则渐变动画不消失
                const forcedShowLoading = interrupted && !isQuickSkuReply;
                return (
                    <MarkDownCard
                        msgData={msgData}
                        key={_item?.sectionId}
                        isLatest={isLatest}
                        page={page}
                        showToolbar={lastMarkdownIndex.current === _index && msgEnd}
                        className={
                            searchReferences?.title && _index === 0
                                ? styles.markDownCardNoBorder
                                : ''
                        }
                        isInterrupted={interrupted}
                        isQuickSkuReply={isQuickSkuReply}
                        header={renderMarkDownCardHeader({title: planProgressDesc, icon: planIcon})}
                    >
                        <View className='imFlowContent'>
                            {/* 渲染步骤组件 */}
                            <RenderComponent data={plan}>
                                {isRenderType2 ? (
                                    <ThinkingChain
                                        plan={plan}
                                        forceNoShow={content || tabList || quickReply?.length > 0}
                                    />
                                ) : (
                                    <MarkdownPlan plan={plan} msgEnd={msgEnd || content} />
                                )}
                            </RenderComponent>
                            {/* 渲染markdown组件 */}
                            <RenderComponent data={!isRenderType2 && content}>
                                <MarkDown
                                    isTypewriter
                                    loadingType=':gradient:'
                                    className={plan?.length ? styles.markdownMargin : ''}
                                    {..._item}
                                    content={content}
                                    forcedShowLoading={forcedShowLoading}
                                    // todo 配料表中一直是true,暂时不关注isFinish字段!
                                    isFinish={msgEnd}
                                    msgEnd={msgEnd}
                                />
                            </RenderComponent>
                            {/* renderType为2时，需要渲染tab组件 */}
                            <RenderComponent data={isRenderType2 && tabList}>
                                <MarkdownTabList
                                    data={tabList}
                                    isTypewriter={dataSource === 'conversation'}
                                    onStart={() => setPrintingTabListCount(prev => prev + 1)}
                                    onComplete={() => setPrintingTabListCount(prev => prev - 1)}
                                />
                            </RenderComponent>
                            <RenderComponent
                                data={isRenderType2 && tabListSummaryCondition && content}
                            >
                                <MarkDown
                                    isTypewriter
                                    loadingType=':gradient:'
                                    markdownClassName={cx(styles.tabListSummary)}
                                    {..._item}
                                    content={content}
                                    isFinish={msgEnd}
                                    msgEnd={msgEnd}
                                    typewriterStartCallback={() => setTabListSummaryEnd(false)}
                                    typewriterSuccessCallback={() => setTabListSummaryEnd(true)}
                                />
                            </RenderComponent>
                            {/* 渲染markdown按钮组件 */}
                            <RenderComponent data={markdownBtn}>
                                <MarkDownBtn
                                    markdownBtn={markdownBtn}
                                    msgId={msgId}
                                    ext={ext}
                                    page={page}
                                />
                            </RenderComponent>
                            {/* 渲染markdown图片组件 */}
                            {
                                <MarkDownImages
                                    images={images}
                                    msgId={msgId}
                                    ext={ext}
                                    isFinish={isFinish || msgEnd}
                                />
                            }
                            {/* 渲染markdown视频组件 */}
                            {
                                <MarkDownVideos
                                    videos={videos}
                                    msgId={msgId}
                                    ext={ext}
                                    isFinish={isFinish || msgEnd}
                                />
                            }
                        </View>
                    </MarkDownCard>
                );
            });
        },
        [
            action,
            msgData,
            searchReferences?.title,
            interrupted,
            renderMarkDownCardHeader,
            isRenderType2,
            quickReply?.length,
            dataSource,
            msgId,
            ext,
            isLatest,
            page,
            tabListSummaryCondition
        ]
    );

    return (
        <View id={imFlowId} className={`${styles.imFlow}`}>
            {/* 渲染思考链一组件 */}
            <RenderComponent data={searchReferences?.title}>
                <MarkDownCard
                    msgData={msgData}
                    key='searchReferencesCard'
                    className={styles.searchReferencesMarkDown}
                >
                    <SearchReferencesCard
                        searchReferences={searchReferences}
                        msgEnd={action === 'end'}
                    />
                </MarkDownCard>
            </RenderComponent>

            {/* 渲染卡片组件 */}
            {renderMsgComponents(list)}

            {/* 渲染反问组件 */}
            <RenderComponent data={rhetoricalQuestions?.length}>
                <RhetoricalQuestions
                    page={page}
                    rhetoricalQuestions={rhetoricalQuestions}
                    msgId={msgId}
                    ext={ext}
                    readonly={!memoIsLastMsg}
                />
            </RenderComponent>

            {/* 渲染追问组件 */}
            <RenderComponent
                data={quickReplyRenderCondition && memoIsLastMsg && quickReply?.length}
            >
                <QuickReply quickReply={quickReply} msgId={msgId} ext={ext} page={page} />
            </RenderComponent>
        </View>
    );
};

ImFlow.displayName = 'ImFlow';
export {type ImFlowProps, type FeedbackPopupDataProps, type ImFlowData};
export default memo(ImFlow);

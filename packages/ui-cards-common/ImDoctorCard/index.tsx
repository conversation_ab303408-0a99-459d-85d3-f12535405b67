import {memo, FC} from 'react';
import cx from 'classnames';
import {View, Text} from '@tarojs/components';
import {WImage} from '@baidu/wz-taro-tools-core';
import {getSystemInfo} from '@baidu/vita-pages-im/src/utils/taro';

import type {ImDoctorCardProps} from './index.d';
import DoctorAvatar from './components/DoctorAvatar';
import DoctorInfo from './components/DoctorInfo';
import DoctorInfoInCombination from './components/DotorInfoInCombination';
import styles from './index.module.less';

const {bigFontSizeClass} = getSystemInfo();

const ImDoctorCard: FC<ImDoctorCardProps> = ({
    data,
    onBtnClick,
    onCardClick,
    isDisplayRec = true,
    mode = 'direct'
}) => {
    return (
        <View className={cx(styles.doctorSkuCardWrapper)}>
            {/* 医生信息 */}
            <View className={cx(styles.doctorCardWrapper)}>
                <View className={cx(styles.doctorCardAvatarWrapper)}>
                    <DoctorAvatar
                        isOnline={data?.isOnline}
                        expertPic={data?.expertPic}
                        mode={mode}
                    />
                </View>
                {mode === 'direct' && (
                    <View className={cx(styles.doctorCardInfoWrapper, 'wz-ml-18')}>
                        <DoctorInfo
                            expertName={data?.expertName}
                            expertDepartment={data?.expertDepartment}
                            expertHospital={data?.expertHospital}
                            expertLevel={data?.expertLevel}
                            attributeTag={data?.attributeTag}
                            expertGoodAt={data?.expertGoodAt}
                            indicatorList={data?.indicatorList}
                            price={data?.price}
                            freeTag={data?.freeTag}
                            pricePre={data?.pricePre}
                            btnInfo={data?.btnInfo}
                            actionInfo={data?.actionInfo}
                            showPortraitTag={data?.showPortraitTag}
                            onBtnClick={onBtnClick}
                            onCardClick={onCardClick}
                        />
                    </View>
                )}
                {/* 组合卡医生信息模式 */}
                {mode === 'combination' && (
                    <View className={cx(styles.doctorCardInfoWrapper, 'wz-ml-24')}>
                        <DoctorInfoInCombination
                            expertName={data?.expertName}
                            expertDepartment={data?.expertDepartment}
                            expertHospital={data?.expertHospital}
                            expertLevel={data?.expertLevel}
                            attributeTag={data?.attributeTag}
                            expertGoodAt={data?.expertGoodAt}
                            indicatorList={data?.indicatorList}
                            price={data?.price}
                            freeTag={data?.freeTag}
                            pricePre={data?.pricePre}
                            btnInfo={data?.btnInfo}
                            actionInfo={data?.actionInfo}
                            showPortraitTag={data?.showPortraitTag}
                            fuDanInfo={data?.fuDanInfo}
                            onBtnClick={onBtnClick}
                            onCardClick={onCardClick}
                        />
                    </View>
                )}
            </View>

            {/* 推荐理由 */}
            {isDisplayRec && data?.recReason && (
                <View
                    className={cx(
                        styles.doctorCardRecommendWrapper,
                        'wz-fs-42, wz-fw-400, wz-mt-36'
                    )}
                >
                    <WImage
                        className={cx(
                            bigFontSizeClass ? styles.bigRecommendIcon : styles.recommendIcon
                        )}
                        src={data?.recReason?.icon}
                    />
                    <Text className={cx(styles.recommendText, 'wz-ml-18')}>
                        {data?.recReason?.text}
                    </Text>
                </View>
            )}
        </View>
    );
};

ImDoctorCard.displayName = 'ImDoctorCard';
export default memo(ImDoctorCard);

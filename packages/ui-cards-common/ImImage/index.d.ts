import {ViewProps} from '@tarojs/components/types/View';
import {CSSProperties} from 'react';
import type {ActionInfo} from '../ImFlow/index.d';

export type ImageMode =
    | 'scaleToFill'
    | 'aspectFit'
    | 'aspectFill'
    | 'widthFix'
    | 'heightFix'
    | 'top'
    | 'bottom'
    | 'center'
    | 'left'
    | 'right'
    | 'topLeft'
    | 'topRight'
    | 'bottomLeft'
    | 'bottomRight';

export interface SimpleImage {
    icon?: string;
    small?: string;
    origin?: string;
}

export interface ImImageContentProps extends SimpleImage {
    list?: SimpleImage[];
    urls?: string[]
}
export interface ImImageProps extends ViewProps {
    // 数据
    data: CardsData<ImImageContentProps>;
    // 是否是im流中图片
    isIm?: boolean;
    // 背景色
    bgColor?: string;
    // 圆角
    borderRadius?: number;
    // 是否展示隐私
    isPrivate?: boolean;
    // 发送状态
    showloading?: boolean;
    mode?: ImageMode;
    // 数组返回一行几个
    lineNum?: number;
    imgStyle?: CSSProperties;
    bigFontSizeClass?: string;
    sendStatus?: 'pending' | 'rejected' | 'fulfilled';
    bdFileMap?: Record<string, string>;
}

export interface CardsData<T> {
    cardStyle?: CardStyle;
    actionInfo?: ActionInfo;
    content?: T;
    ext?: {
        [key: string]: string | number;
    };
}

export interface CardStyle {
    needHead?: boolean;
    renderType?: number;
    width?: number;
    height?: number;
    positionType?: number;
    isHidden?: boolean;
    disablePreview?: boolean;
}

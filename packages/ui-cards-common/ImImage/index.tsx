import {useCallback, useMemo, memo, useEffect, useState} from 'react';
import {pxTransform, previewImage} from '@tarojs/taro';
import {WisePic} from '@baidu/wz-taro-tools-icons';
import {WImage} from '@baidu/wz-taro-tools-core';
import {View} from '@tarojs/components';
import cx from 'classnames';

import type {ImImageProps, ImImageContentProps, SimpleImage} from './index.d';
import {getImageInfo} from './utils/getImageInfo';
import styles from './index.module.less';

const INIT_BACKGROUND = 'rgba(0, 0, 0, 0.03)';

const ImImage = (props: ImImageProps) => {
    const {
        data,
        isIm = true,
        bgColor: propsBgColor,
        borderRadius = 27,
        isPrivate = false,
        showloading = false,
        mode = 'aspectFit',
        lineNum = 3,
        imgStyle = {},
        bdFileMap = {}
    } = props;
    const {content, cardStyle} = data;
    const {small = '', origin = '', list = [], urls = []} = (content || {}) as ImImageContentProps;
    const [changeImageInfo, setChangeImageInfo] = useState<{width: number; height: number}>({
        width: 0,
        height: 0
    });

    const bgColor = useMemo(() => {
        // 透明底纹图片不需要样式
        if (cardStyle?.renderType === 1) {
            return '';
        }

        return propsBgColor ? propsBgColor : INIT_BACKGROUND;
    }, [cardStyle?.renderType, propsBgColor]);

    // 单图封面
    const [cover, setCover] = useState('');

    const handleStyle = useCallback(async () => {
        try {
            // 如果cardStyle中传了宽度和高度，用cardStyle中的，否则用图片信息的
            if (cardStyle?.width && cardStyle?.height) {
                setChangeImageInfo({
                    width: cardStyle?.width,
                    height: cardStyle?.height
                });

                return;
            }
            let path = '';
            if (content?.small) {
                path = content?.small || '';
            } else if (content?.list && content?.list?.length) {
                path = content?.list[0].origin || '';
            }
            const res = await getImageInfo({path});

            const {width = 360, height = 360} = res;
            setChangeImageInfo({
                width,
                height
            });
        } catch (error) {
            setChangeImageInfo({
                width: 360,
                height: 360
            });
        }
    }, [cardStyle?.width, cardStyle?.height, content]);

    useEffect(() => {
        handleStyle();
    }, [handleStyle]);

    useEffect(() => {
        if (cover?.split('?')?.[0] !== (small || origin)?.split('?')?.[0]) {
            // 防止重复渲染导致封面闪烁
            setCover(small || origin);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [small, origin]);

    // 数组转二维数组 方便flex 换行渲染
    const imgList = useMemo(() => {
        return list.reduce((acc, current) => {
            const lastChunk = acc[acc.length - 1] as SimpleImage[];
            if (lastChunk && lastChunk.length < lineNum) {
                lastChunk.push(current);
            } else {
                acc.push([current]);
            }
            return acc;
        }, [] as SimpleImage[][]);
    }, [list, lineNum]);

    // 图片宽高比
    const {width = 360, height = 360} = changeImageInfo;
    const radio = width / height < 1;

    const wrapStyle = useMemo(() => {
        // 非IM图片用实际宽高
        if (!isIm) {
            return {
                width: width ? pxTransform(width) : '100%',
                height: height ? pxTransform(height) : 'auto'
            };
        }
        // 最大宽高465， 最小宽度360
        const maxPX = 465;
        const minPX = 360;
        const radioWidth = width < 360 ? minPX : width > 465 ? maxPX : width;

        return {
            width: width ? pxTransform(radio ? (width * maxPX) / height : width) : '100%',
            height: height
                ? pxTransform(radio || width < 360 ? height : (height * radioWidth) / width)
                : 'auto',
            maxHeight: pxTransform(465),
            maxWidth: pxTransform(465),
            paddingBottom: isPrivate ? pxTransform(74) : ''
        };
    }, [isIm, width, radio, height, isPrivate]);

    const handleImage = useCallback(
        (url: SimpleImage) => {
            if (cardStyle?.disablePreview) {
                return false;
            }
            let imgSrc: string = url.origin || url.small || '';
            let trueUrls = urls;
            
            if (imgSrc) {
                if (process.env.TARO_ENV === 'swan' && imgSrc?.indexOf('bdfile:') > -1) {
                    imgSrc = bdFileMap?.[imgSrc] || imgSrc;
                    trueUrls = urls.map(item => {
                        if(bdFileMap?.[item]){
                            return bdFileMap?.[item];
                        }else{
                            return item;
                        }
                        }
                    )
                }
                
                previewImage({
                    current: imgSrc, // 当前显示图片的http链接
                    // 需要预览的图片http链接列表
                    urls: trueUrls?.length > 0 ? trueUrls : [imgSrc]
                });
            }
        },
        [cardStyle?.disablePreview, bdFileMap, urls]
    );

    return (
        <>
            {/* 单图渲染 */}
            {cover && (
                <View
                    className={styles['im-image']}
                    style={{
                        ...wrapStyle,
                        minWidth: isPrivate ? pxTransform(360) : '',
                        justifyContent: isPrivate ? 'center' : 'left',
                        backgroundColor: bgColor,
                        overflow: 'hidden',
                        borderRadius: pxTransform(borderRadius)
                    }}
                >
                    <View className={styles['im-image__content']}>
                        <WImage
                            src={cover}
                            mode={mode}
                            style={{
                                display: 'flex',
                                justifyContent: 'center',
                                ...wrapStyle,
                                paddingBottom: 0
                            }}
                            fallback={
                                <View className='wz-flex'>
                                    <WisePic size={105} color='#dcdee0' />
                                    <View className='wz-fs-36 wz-pt-24'>加载失败</View>
                                </View>
                            }
                            onClick={() => {
                                handleImage({small, origin});
                            }}
                        />
                        {/* 展示loading */}
                        {showloading && (
                            <View
                                className={cx(
                                    'wz-flex wz-col-center wz-row-center',
                                    styles['im-image__mask']
                                )}
                            >
                                <View
                                    className={cx(
                                        'wz-flex wz-col-center wz-row-center',
                                        styles['im-image__sendStatus'],
                                        styles['im-image__sendStatus--loading']
                                    )}
                                />
                            </View>
                        )}
                    </View>
                    {/* 展示隐私安全保护 */}
                    {isPrivate && (
                        <View
                            className={cx(
                                styles['im-image__private'],
                                'wz-fs-36 wz-text-center wz-flex wz-col-center wz-row-center'
                            )}
                        >
                            <WImage
                                className={styles['im-image__private__waterMark']}
                                mode='aspectFit'
                                src='https://med-fe.cdn.bcebos.com/mercury/anquan.png'
                            />
                            <View className={cx(styles['im-image__private__text'], 'wz-fs-36')}>
                                平台保证隐私安全
                            </View>
                        </View>
                    )}
                </View>
            )}
            {/* 多图渲染 */}
            {imgList &&
                imgList?.length > 0 &&
                imgList.map((item, index) => {
                    return (
                        <View
                            key={`ImCommonOut-${index}`}
                            className={cx(
                                'wz-flex',
                                'wz-row-between',
                                index !== 0 ? 'wz-mt-36' : ''
                            )}
                        >
                            {item.map((i, index) => {
                                return (
                                    <View
                                        key={`ImCommonIn-${index}`}
                                        className={styles['im-image']}
                                        style={{
                                            ...wrapStyle,
                                            ...imgStyle,
                                            minWidth: isPrivate ? pxTransform(360) : '',
                                            justifyContent: isPrivate ? 'center' : 'left',
                                            backgroundColor: bgColor,
                                            overflow: 'hidden',
                                            borderRadius: pxTransform(borderRadius)
                                        }}
                                    >
                                        <View className={styles['im-image__content']}>
                                            <WImage
                                                src={i.small || i.origin}
                                                mode={mode}
                                                style={{
                                                    display: 'flex',
                                                    justifyContent: 'center',
                                                    ...wrapStyle
                                                }}
                                                fallback={
                                                    <View className='wz-flex'>
                                                        <WisePic size={105} color='#dcdee0' />
                                                        <View className='wz-fs-36 wz-pt-24'>
                                                            加载失败
                                                        </View>
                                                    </View>
                                                }
                                                onClick={() => {
                                                    handleImage(i);
                                                }}
                                            />
                                        </View>
                                    </View>
                                );
                            })}
                            {/* 不够三个给补一个占位 方便flex布局 */}
                            {item?.length < lineNum && (
                                <View
                                    style={{
                                        ...wrapStyle
                                    }}
                                />
                            )}
                        </View>
                    );
                })}
        </>
    );
};

export default memo(ImImage, pre => {
    // 图片发送成功，不再重新渲染
    if (pre?.sendStatus === 'fulfilled') {
        return true;
    }

    return false;
});

import {PATH_MAP} from '../../../../path/index';
import {H5_HOST} from '../../../../models/apis/host';
import {getPageBaseUrl} from '../../../../utils/index';
import {OpenType} from '../main';

const independentSubSign = '/vita';
const isDev = process.env.NODE_ENV === 'development';

const getPlatform =
    require('../../../../utils/basicAbility/adWebView/getPlatform.ts').default || {};

const isBaiduApp = getPlatform.isBaiduApp();

const handleMarsSubpackage = (url: string) => {
    const reg = new RegExp(
        '\\/?(sporem|uha/ask|mfollow/materials|mall|tuanjian|vas|' +
            'decision|aihub|wenzhen|guahao|health-main)\\/pages[\\w-/]+'
    );
    if (reg.test(url)) {
        if (url.startsWith('http://') || url.startsWith('https://')) {
            return url;
        }
        return `${isDev ? H5_HOST : getPageBaseUrl()}${url}`;
    }
    return null;
};

const handleH5NewPath = (url: string) => {
    if (/\/?(vita)\/pages[\w-/]+/.test(url)) {
        return url.substring(independentSubSign.length);
    }
    return null;
};

const setNewUrl = (url: string) => {
    const urlObj = url.split('?');
    const path = urlObj[0].substring(0, 1) !== '/' ? `/${urlObj[0]}` : urlObj[0];

    const pathRoute: string = PATH_MAP[path];
    if (pathRoute) {
        return `${pathRoute}?${urlObj[1]}`;
    }

    const marsResult = handleMarsSubpackage(url);
    if (marsResult) return marsResult;

    const h5Result = handleH5NewPath(url);
    if (h5Result) return h5Result;

    if (/^baiduboxapp.*/.test(url)) {
        if (isBaiduApp) {
            return url;
        }
        const matchs = url.match(/baiduboxapp:\/\/swan\/(\w+)\/(.+)/) || [];
        return `https://mbd.baidu.com/ma/landingpage?t=smartapp_share&scenario=share&appid=${matchs[1]}&url=${encodeURIComponent(matchs[2])}`;
    }

    return url;
};

export const commonRedirectUrlFn = ({
    url,
    openType
}): Promise<{url: string; openType: OpenType}> => {
    return new Promise((resolve, reject) => {
        const _url = setNewUrl(url);

        try {
            resolve({
                url: _url,
                openType
            });
        } catch (err) {
            reject(err);
        }
    });
};

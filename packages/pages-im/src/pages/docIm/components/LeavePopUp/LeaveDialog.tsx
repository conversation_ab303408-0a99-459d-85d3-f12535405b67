import {View} from '@tarojs/components';
import {memo, FC, useState, useEffect} from 'react';
import {Button, Dialog} from '@baidu/wz-taro-tools-core';

import {ubcCommonClkSend, ubcCommonViewSend} from '../../../../utils/generalFunction/ubc';

import type {ILeaveProps} from './index.d';

const LeaveDialog: FC<ILeaveProps> = (props: ILeaveProps) => {
    const {leaveInfo, closeDialog} = props;
    const {title, content, btns, position = 'left'} = leaveInfo || {};

    const [open, setOpen] = useState(false);

    useEffect(() => {
        setOpen(!!content);
    }, [content]);
    useEffect(() => {
        ubcCommonViewSend({
            value: 'default_leave'
        });
    }, []);

    const btnTap = type => {
        if (type === 'cancel') {
            closeDialog && closeDialog(true);
        } else if (type === 'confirm') {
            closeDialog && closeDialog(false);
        }
        ubcCommonClkSend({
            value: `default_leave_${type as string}`
        });
    };

    return (
        <Dialog 
            open={open} 
            style={{zIndex: 1111}} 
            // TODO: 明确是否支持该属性；@wanghaoyu08
            // backdrop={{style: {zIndex: 1110}}}
        >
            <Dialog.Header>
                <View className='wz-fs-72'>{title}</View>
            </Dialog.Header>
            <Dialog.Content>
                <View className={`wz-fs-45 wz-text-${position}`} style={{lineHeight: '1.6'}}>
                    {content}
                </View>
            </Dialog.Content>
            <Dialog.Actions>
                {btns?.map((btn, idx) => {
                    return (
                        <Button
                            key={idx}
                            onClick={() => btnTap(btn.type)}
                            style={{
                                color: `${btn.type === 'confirm' ? '' : 'rgb(0, 200, 200)'}`
                            }}
                        >
                            {btn.text}
                        </Button>
                    );
                })}
            </Dialog.Actions>
        </Dialog>
    );
};

export default memo(LeaveDialog);

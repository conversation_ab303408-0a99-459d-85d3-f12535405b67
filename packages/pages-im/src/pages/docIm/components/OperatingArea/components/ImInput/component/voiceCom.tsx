// Author: z<PERSON><PERSON>yu03
// Date: 2025-02-14 17:53:19
// Description: 语音组件（包含长按按钮和语音蒙层）

import {View} from '@tarojs/components';
import React, {memo, useCallback, useRef, useState} from 'react';
import cx from 'classnames';
import {throttle} from 'lodash-es';
import {eventCenter} from '@tarojs/taro';

import useTouchEvent from '../../../../../../../hooks/common/useTouchEvent';

import VoiceModal from './voiceModal/index';
import type {VoiceComProps} from './index.d';
import styles from './index.module.less';


const ImTriageVoice = memo((props: VoiceComProps) => {
    const {handleChangeIcon} = props;

    const [open, setOpen] = useState(false);
    const popRef = useRef(null);

    const handleLongPress = useCallback(() => {
        setOpen(true);
        eventCenter.trigger('stopTTS');
    }, []);

    const handleClose = useCallback(() => {
        setOpen(false);
    }, []);

    const {info, onTouchFn} = useTouchEvent({
        // TODO:unknown暂时添加类型
        onTouchEnd: (msg: unknown) => {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-expect-error
            popRef && popRef?.current?.handleTouchEnd(msg);
        },
        onTouchStart: (msg: unknown) => {
            // onTouchStart(_info);
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-expect-error
            popRef && popRef?.current?.handleTouchStart(msg);
        },
        isStopEvent: true,
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-expect-error
        onTouchMove: throttle(() => (popRef && popRef?.current?.handleTouchMove(info), 100))
    });

    return (
        <>
            {/* eslint-disable-next-line @typescript-eslint/ban-ts-comment */}
            {/* @ts-expect-error */}
            <View
                onLongTap={handleLongPress}
                onLongPress={handleLongPress}
                {...onTouchFn}
                className={cx(
                    styles.imTriageVoice,
                    'wz-flex'
                    // 'wz-mb-33'
                )}
            >
                按住开始说话
            </View>
            {/* eslint-disable-next-line @typescript-eslint/ban-ts-comment */}
            {/* @ts-expect-error */}
            <VoiceModal
                ref={popRef}
                open={open}
                handleClose={handleClose}
                handleChangeIcon={handleChangeIcon}
            />
        </>
    );
});

ImTriageVoice.displayName = 'ImTriageVoice';

export default ImTriageVoice;

import {SSEProcessor} from 'sse-kit/lib/bundle.weapp.esm';

import {API_HOST} from '../../../../models/apis/host';
import {createTriageStreamConversation} from '../../../apis/vtui';

import editConf from '../../../../utils/basicAbility/comonRequest/utils/editConf';

import type {SSEResponseType} from '../../../../store/triageStreamAtom/index.type.ts';

import type {ConversationSSEParams} from './index.d';

export type SSEProcessorInstance<T extends object> = InstanceType<typeof SSEProcessor<T>>;

export const conversationSSE = async (args: ConversationSSEParams): Promise<SSEProcessorInstance<SSEResponseType>> => {
    const {params, ops} = args;
    
    const conf = {
        url: `${API_HOST}${createTriageStreamConversation}`,
        method: 'POST' as const,
        data: {...params},
        needTransparentWhiteListParams: true
    };
    const decoratedConf = await editConf(conf);

    return new SSEProcessor<SSEResponseType>({
        url: decoratedConf.url as `https://${string}`,
        method: 'POST',
        enableConsole: false,
        headers: decoratedConf.header as Headers,
        reqParams: decoratedConf.data,
        onComplete: ops.onComplete,
        onError: ops.onError,
        onHeadersReceived: ops.onHeadersReceived
    });
};


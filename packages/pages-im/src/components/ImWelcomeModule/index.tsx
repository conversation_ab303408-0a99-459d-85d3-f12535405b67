import {eventCenter} from '@tarojs/taro';
import {type FC, memo, useCallback, useEffect, useLayoutEffect, useMemo, useState} from 'react';
import cx from 'classnames';
import {debounce} from 'lodash-es';
import {View} from '@tarojs/components';
import {Portal} from '@baidu/vita-ui-cards-common';
import QuickReply from '@baidu/vita-ui-cards-common/ImFlow/components/QuickReply';

import TipImgUploadPopup from '../pagePopup/TipImgUploadPopup';

import {ubcCommonViewSend, ubcCommonClkSend} from '../../utils/generalFunction/ubc';

import {useCapsuleToolsController} from '../../hooks/useCapsuleTools';
import {useGetSessionMsgIds} from '../../hooks/triageStream/dataController';

import {useSetImageSource} from '../../hooks/triageStream/pageDataController';
import {useUpdateImWelcomeMouleDisplayStatus} from '../../hooks/triageStream/useImWelcomeMoule';

import type {MsgId} from '../../typings';
import type {IPicProps} from '../../typings/upload';
import type {CapsulesToolsType} from '../../store/triageStreamAtom/index.type';
import type {SceneTypeOfParams} from '../../models/services/triageStream/sse/index.d';
import type {WelcomeContent} from './index.d';

import styles from './index.module.less';

interface IProps {
    curMsgId: MsgId;
    visibleRange?: number; // 可视区域边界阈值 默认0.7；
    onScrollIntoView?: () => void; // 进入可视区域回调
    onScrollOutOfView?: () => void; // 离开可视区域回调
    data: {
        content: WelcomeContent;
    };
}

/**
 *
 * @description 判断是否为占位元素
 *
 */
function isPlaceholderItem(item) {
    return item && typeof item === 'object' && item.isPlaceholder === true;
}

/**
 *
 * @description 补齐数组长度为 2 或 3 的倍数，保证多行渲染时的元素对齐
 * @param originalList 原始数组
 * @returns 补齐后的数组，占位元素标记为 isPlaceholder: true
 */
function padArrayForAlignment<T>(originalList: T[]): (T | {isPlaceholder: boolean})[] {
    if (!originalList?.length || [1, 2, 3, 4].includes(originalList.length)) {
        return originalList;
    }

    const displayList: (T | {isPlaceholder: boolean})[] = [...originalList];
    const remainder2 = originalList.length % 2;
    const remainder3 = originalList.length % 3;

    if (remainder2 === 0 || remainder2 === 1) {
        const needToAdd = remainder2 === 0 ? 0 : 2 - remainder2;
        for (let i = 0; i < needToAdd; i++) {
            displayList.push({isPlaceholder: true});
        }
    } else {
        const needToAdd = remainder3 === 0 ? 0 : 3 - remainder3;
        for (let i = 0; i < needToAdd; i++) {
            displayList.push({isPlaceholder: true});
        }
    }

    return displayList;
}

const docIpImg = 'https://med-fe.cdn.bcebos.com/wz-mini%2FImWelcomeModule%2FdocIp.png';

const ImWelcomeModule: FC<IProps> = props => {
    // const {data, curMsgId, visibleRange = 0.8, onScrollIntoView, onScrollOutOfView} = props;
    const {data, curMsgId} = props;
    // const isVisible = useRef<boolean | undefined>();
    const [popupOpenStatus, setPopupOpenStatus] = useState(false);
    const [sceneType, setSceneType] = useState<SceneTypeOfParams | undefined>();
    const [tipsData, setTipsData] = useState<CapsulesToolsType['instruction'] | undefined>();

    const {updateWzMessage, sendImgGuideMsg} = useCapsuleToolsController();
    const {setImageSource} = useSetImageSource();
    // const {msgIds: effectiveMsgId} = useGetImWelcomeMouleEffectiveMsgId();
    const {updateImWelcomeMoule} = useUpdateImWelcomeMouleDisplayStatus();
    const {msgIds} = useGetSessionMsgIds(curMsgId);
    const isLastMsg = useMemo(() => {
        return curMsgId === msgIds[msgIds.length - 1];
    }, [curMsgId, msgIds]);

    // const needObserve = useMemo(() => {
    //     return curMsgId === effectiveMsgId;
    // }, [curMsgId, effectiveMsgId]);

    const handleSkuClick = useCallback(
        (item: WelcomeContent['serviceList'][number]) => {
            const {interaction, interactionInfo} = item.actionInfo;
            if (interaction === 'sendMsg') {
                updateWzMessage(interactionInfo, {
                    sceneType: interactionInfo?.sceneType as SceneTypeOfParams
                });
            } else if (interaction === 'sendImg') {
                setTipsData(item?.instruction);
                setPopupOpenStatus(true);
                setSceneType(interactionInfo?.sceneType as SceneTypeOfParams);
            } else if (interaction === 'sendUploadImgGuidance'){
                sendImgGuideMsg(interactionInfo, item?.title, item?.type);
            }

            ubcCommonClkSend({
                value: `ImWelcomeModule_${item.type}`,
                ext: {
                    product_info: {
                        msgId: curMsgId || '',
                        tools: data?.content?.serviceList?.map(item => item.type).join('_')
                    }
                }
            });
        },
        [curMsgId, data?.content?.serviceList, updateWzMessage]
    );

    const throttledHandleSkuClick = useMemo(() => debounce(handleSkuClick, 300), [handleSkuClick]);

    const closeUploadPopup = useCallback(() => {
        setPopupOpenStatus(false);
        setTipsData(undefined);
    }, []);

    const onSelectedPics = useCallback((res: IPicProps[], {sceneType}) => {
        try {
            eventCenter.trigger('handleSelectImage', {
                imgList: res,
                sceneType,
                _symbol: 'ImWelcomeModule'
            });
        } catch (error) {
            console.error('ImWelcomeModule onSelectedPics error: ', error);
        }
    }, []);

    const genSingleSku = useMemo(() => {
        if (!data?.content?.serviceList?.length) return null;
        const [item] = data?.content?.serviceList || [];
        const {icon, title, subTitle, btn} = item;

        return (
            <View
                className={cx(styles.singleSkuContainer, 'wz-flex')}
                onClick={() => throttledHandleSkuClick(item)}
            >
                <View
                    className={cx(styles.icon, 'wz-mr-36')}
                    style={{backgroundImage: `url(${icon})`}}
                />
                <View className={styles.skuCon}>
                    <View className={cx(styles.skuTitle, 'wz-fs-48 wz-taro-ellipsis')}>
                        {title}
                    </View>
                    <View className={cx(styles.skuDesc, 'wz-fs-42 wz-fw-400 wz-taro-ellipsis')}>
                        {subTitle}
                    </View>
                </View>
                <View className={cx(styles.skuBtn, 'wz-fs-39')}>{btn?.value}</View>
            </View>
        );
    }, [data?.content?.serviceList, throttledHandleSkuClick]);

    const genMultiSku = useMemo(() => {
        if (!data?.content?.serviceList?.length) return null;

        const originalList = data?.content?.serviceList;
        const displayList = padArrayForAlignment(originalList);

        // 根据 displayList 长度决定每行显示个数：2或4时一行2个，其他情况一行3个
        const itemsPerRow = displayList.length === 2 || displayList.length === 4 ? 2 : 3;

        return (
            <View className={cx(styles.multiSkuContainer, 'wz-flex')}>
                {displayList.map((item, index) => {
                    // 如果是占位元素，渲染透明的占位块
                    if (isPlaceholderItem(item)) {
                        return (
                            <View
                                className={cx(
                                    styles.skuItem,
                                    styles[`skuItemOf${itemsPerRow}PerRow`]
                                )}
                                key={`placeholder-${index}`}
                                style={{visibility: 'hidden'}}
                            >
                                <View className={cx(styles.icon)} />
                                <View
                                    className={cx(styles.skuTitle, 'wz-fs-42 wz-taro-ellipsis')}
                                />
                            </View>
                        );
                    }

                    return (
                        <View
                            className={cx(
                                styles.skuItem,
                                'wz-flex',
                                styles[`skuItemOf${itemsPerRow}PerRow`]
                            )}
                            key={`item-${index}`}
                            onClick={() =>
                                throttledHandleSkuClick(
                                    item as WelcomeContent['serviceList'][number]
                                )
                            }
                        >
                            <View
                                className={cx(styles.icon, 'wz-mb-36')}
                                style={{
                                    backgroundImage: `url(${(item as WelcomeContent['serviceList'][number]).icon})`
                                }}
                            />
                            <View className={cx(styles.skuTitle, 'wz-fs-42 wz-taro-ellipsis')}>
                                {(item as WelcomeContent['serviceList'][number]).title}
                            </View>
                        </View>
                    );
                })}
            </View>
        );
    }, [data?.content?.serviceList, throttledHandleSkuClick]);

    const genSku = useMemo(() => {
        return (
            <View className={cx(styles.moduleSku, 'wz-br-63')}>
                {data?.content?.serviceList?.length > 1 ? genMultiSku : genSingleSku}
            </View>
        );
    }, [data?.content?.serviceList?.length, genMultiSku, genSingleSku]);

    const imWelcomeModuleId = useMemo(() => {
        return `imWelcomeModule_${curMsgId}`;
    }, [curMsgId]);

    // 当前组件被渲染时，前置更新ImWelcomeMoule的默认显示状态为true，
    // 避免依赖该状态的CapsuleTools组件的展示出现闪烁
    useLayoutEffect(() => {
        updateImWelcomeMoule(true);
    }, [updateImWelcomeMoule]);

    // useEffect(() => {
    //     if (process.env.TARO_ENV !== 'h5' && needObserve) {
    //         const observer = createIntersectionObserver(getCurrentInstance().page as PageInstance, {
    //             observeAll: true,
    //             thresholds: [0, 0.1, 0.25, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1]
    //         });

    //         nextTick(() => {
    //             observer.relativeToViewport().observe(`#${imWelcomeModuleId}`, res => {
    //                 if (
    //                     typeof res.intersectionRatio === 'number' &&
    //                     res.intersectionRatio > visibleRange
    //                 ) {
    //                     if ([undefined, false].includes(isVisible.current)) {
    //                         isVisible.current = true;
    //                         onScrollIntoView?.();
    //                     }
    //                 } else if (
    //                     typeof res.intersectionRatio === 'number' &&
    //                     res.intersectionRatio <= visibleRange
    //                 ) {
    //                     if ([undefined, true].includes(isVisible.current)) {
    //                         isVisible.current = false;
    //                         onScrollOutOfView?.();
    //                     }
    //                 }
    //             });
    //         });

    //         return () => {
    //             observer.disconnect();
    //         };
    //     }
    // }, [onScrollIntoView, onScrollOutOfView, visibleRange, needObserve, imWelcomeModuleId]);

    // useEffect(() => {
    //     if (process.env.TARO_ENV === 'h5' && needObserve) {
    //         let observer: IntersectionObserver | null = null;
    //         // 监听用户交互事件
    //         const handleIntersection = ([entry]: IntersectionObserverEntry[]) => {
    //             isVisible.current = entry.intersectionRatio > visibleRange;

    //             if (isVisible.current) {
    //                 // eslint-disable-next-line no-console
    //                 console.info(`${imWelcomeModuleId}: 进入可视区域`);
    //                 onScrollIntoView?.();
    //             } else {
    //                 // eslint-disable-next-line no-console
    //                 console.info(`${imWelcomeModuleId}: 离开可视区域`);
    //                 onScrollOutOfView?.();
    //             }
    //         };

    //         const initObserver = () => {
    //             if (observer) observer.disconnect();

    //             const target = document.querySelector(`#${imWelcomeModuleId}`);
    //             if (!target) {
    //                 // eslint-disable-next-line no-console
    //                 console.warn('未找到目标元素#imWelcomeModule');
    //                 return;
    //             }

    //             observer = new IntersectionObserver(handleIntersection, {
    //                 threshold: [visibleRange]
    //             });
    //             observer.observe(target);
    //         };

    //         // 延迟初始化，等待DOM完全加载
    //         const timer = setTimeout(initObserver, 300);

    //         // 清理函数
    //         return () => {
    //             if (observer) observer.disconnect();
    //             clearTimeout(timer);
    //         };
    //     }
    // }, [onScrollIntoView, onScrollOutOfView, visibleRange, needObserve, imWelcomeModuleId]);

    useEffect(() => {
        ubcCommonViewSend({
            value: 'ImWelcomeModule',
            ext: {
                product_info: {
                    msgId: curMsgId || '',
                    tools: data?.content?.serviceList?.map(item => item.type).join('_')
                }
            }
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // 组件卸载时取消节流函数
    useEffect(() => {
        return () => {
            throttledHandleSkuClick.cancel();
        };
    }, [throttledHandleSkuClick]);

    return (
        <View className={cx(styles.imWelcomeModuleWrapper)}>
            <View id={imWelcomeModuleId} className={cx(styles.imWelcomeModule, 'wz-br-63')}>
                <View
                    className={cx(
                        styles.imWelcomeModuleTitleConainer,
                        'wz-pl-45 wz-pr-30 wz-pb-54'
                    )}
                >
                    <View className={cx(styles.imWelcomeModuleTitle, 'wz-fs-54')}>
                        {data?.content?.title}
                    </View>
                    <View className={cx(styles.imWelcomeModuleText, 'wz-fs-42 wz-fw-400')}>
                        {data?.content?.subTitle}
                    </View>
                </View>
                <View
                    className={styles.docDisplayImg}
                    style={{backgroundImage: `url(${data?.content?.docImg || docIpImg})`}}
                />
                {genSku}
            </View>
            {isLastMsg && data?.content?.quickReplyTitle && data?.content?.quickReply?.length && (
                <View>
                    <View className={cx(styles.replyText, 'wz-mt-51 wz-mb-12 wz-pl-15 wz-fs-39')}>
                        {data?.content?.quickReplyTitle}：
                    </View>
                    <QuickReply
                        msgId={curMsgId}
                        page='im'
                        ubcValue='ImWelcomeQuickReply'
                        quickReply={data?.content?.quickReply}
                    />
                </View>
            )}
            <Portal>
                <TipImgUploadPopup
                    sceneType={sceneType}
                    tipsData={tipsData}
                    open={popupOpenStatus}
                    uploadOps={['album', 'camera']}
                    onSelectedPics={onSelectedPics}
                    closeUploadPopup={closeUploadPopup}
                />
            </Portal>
        </View>
    );
};

export default memo(ImWelcomeModule);

// Author: z<PERSON><PERSON>yu03
// Date: 2025-06-17 21:26:26
// Description: 图片组件
import {useCallback, useEffect, useState} from 'react';
import {View, Text} from '@tarojs/components';
import {eventCenter, previewImage} from '@tarojs/taro';
import {WImage, Loading} from '@baidu/wz-taro-tools-core';
import cx from 'classnames';

import {ubcCommonViewSend, ubcCommonClkSend} from '../../../../utils/generalFunction/ubc';
import {useGetImageSource, useSetImageSource} from '../../../../hooks/triageStream/pageDataController';
import {uploadFileToBos, previewPic} from '../../../../utils/basicAbility/upload';

import {getBdFileMapAtom} from '../../../../store/triageStreamAtom/bdFileMapAtom';

import type {SceneTypeOfParams} from '../../../../models/services/triageStream/sse/index.d';

import styles from './index.module.less';

import type {ImageListProps} from './index.d';

// 继续上传icon
const continueUploadIocn = 'https://med-fe.cdn.bcebos.com/vita/vita_addPlus.png';

// 删除图片按钮
const deleteImgIcon = 'https://med-fe.cdn.bcebos.com/vita/vita_closeIconDot.png';

const ImageListTools = (props: ImageListProps) => {
    const {bucketConfName, imgList = [], statusList, isMultiple,setIsMultiple, setImgList, setStatusList} = props;

    const {setImageSource} = useSetImageSource();
    const {imageSource} = useGetImageSource();
    const [showAddImageIcon, setShowAddImageIcon] = useState(true);

    const isCheckSameSource = useCallback((sceneType: string)=>{
        if(sceneType?.indexOf('_') > -1){
            const [,scene] = sceneType?.split('_')

            return imageSource?.indexOf(scene) > -1;
        };

        if(imageSource?.indexOf('_') > -1){
            const [,scene] = imageSource?.split('_')

            return sceneType?.indexOf(scene) > -1;
        };

        return imageSource === sceneType;
    },[imageSource])

    const isCheckShowAddIcon = useCallback((picCount)=>{
        if(picCount && picCount === 1){
            setIsMultiple(false);
        } else {
            setIsMultiple(true);
        }
    },[imgList, setIsMultiple])

    // 预览图片
    const handleViewPic = useCallback(url => {
        if (process.env.TARO_ENV === 'swan') {
            const bdFileMap = getBdFileMapAtom();
            previewImage({
                current: bdFileMap[url],
                urls: [bdFileMap[url]]
            });
        } else {
            previewImage({
                current: url,
                urls: [url]
            });
        }
    }, []);

    /**
     * @description 用于处理上传状态
     */
    const transUploadStatus = useCallback(
        (imgList, status, isRtry?:boolean) => {
            const newStatusList = [] as typeof statusList;
            imgList?.map(item => {
                newStatusList.unshift(status);
                return (item.uploadStatus = status);
            });
            if(isRtry){
                setStatusList([...newStatusList])
            }else{
                setStatusList([...newStatusList, ...statusList]);
            }
        },
        [statusList, setStatusList]
    );

    const handleUpload = useCallback(async (sceneType?: SceneTypeOfParams, btnType?: 'album' | 'camera' | '', picCount?: number) => {

        // 兜底支持上传一张
        const extraImgLength = (picCount ?? 1) - (imgList?.length || 0);
        // if (extraImgLength <= 0) {
        //     return;
        // }
        
        const count = extraImgLength > 0 ? extraImgLength : 1;
        const preData = await previewPic({count,btnType});
        if (!preData || !Object.keys(preData)?.length) return;

        // 非同能力，上传图片先清空，再上传 例如：【报告单解读】和【皮肤病检测】属于非同能力；引导卡中的【报告单解读】和胶囊位的【报告单解读】属于同能力
        if(sceneType && !isCheckSameSource(sceneType)) {
            transUploadStatus(preData, 'pending', true);
            setImgList([...preData]);
            try {
                const picData = await uploadFileToBos(preData || [], {
                    count,
                    bucketConfName
                });
                transUploadStatus(picData, 'success', true);
                setImgList([...picData]);
            } catch (error) {
                console.error('上传失败:', error);
                transUploadStatus(preData, 'failed', true);
                setImgList([...preData]);
            }
        }else{
            // 处理状态为上传中
            transUploadStatus(preData, 'pending');
            setImgList([...preData, ...imgList]);
            try {
                const picData = await uploadFileToBos(preData || [], {
                    count,
                    bucketConfName
                });
                transUploadStatus(picData, 'success');
                setImgList([...picData, ...imgList]);
            } catch (error) {
                console.error('上传失败:', error);
                transUploadStatus(preData, 'failed');
                setImgList([...preData, ...imgList]);
            }
        }
       // 设置是否支持多选
        isCheckShowAddIcon(picCount);
        sceneType && setImageSource(sceneType); // 用于上传图片时的场景类型，默认为空字符串
        ubcCommonClkSend({
            value: 'continueUpload_click'
        });
        
        
    }, [imgList, statusList, bucketConfName,isCheckSameSource, transUploadStatus, isCheckShowAddIcon, setImageSource, setImgList, setStatusList]);

    const handleRetryUpload = useCallback(
        async (item, index) => {
            // 更新状态为上传中
            const newImgList = [...imgList];
            const newStatusList = [...statusList];
            newImgList[index] = {...item, uploadStatus: 'pending'};
            newStatusList[index] = 'pending';
            // newImgList.splice(index,1,{...item, uploadStatus: 'pending'})
            setImgList(newImgList);
            setStatusList(newStatusList);

            // 重新上传
            try {
                const picData = await uploadFileToBos([item], {
                    count: 1,
                    bucketConfName
                });
                const newList = [...newImgList];
                const newStatus = [...newStatusList];
                newList[index] = {...picData[0], uploadStatus: 'success'};
                newStatus[index] = 'success';
                setImgList(newList);
                setStatusList(newStatus);
            } catch (error) {
                // 上传失败，保持失败状态
                const newList = [...newImgList];
                const newStatus = [...newStatusList];
                newList[index] = {...item, uploadStatus: 'failed'};
                newStatus[index] = 'failed';
                setImgList(newList);
                setStatusList(newStatus);
                console.error('上传失败:', error);
            }
        },
        [imgList, statusList, bucketConfName, setImgList, setStatusList]
    );

    const loadComponent = useCallback(
        item => {
            return (
                <View className={styles.loadingCom}>
                    <WImage
                        onClick={() => handleViewPic(item?.path)}
                        className={styles.loadingImg}
                        shape='rounded'
                        src={item?.path}
                    />
                    <View className={styles.coverView} />
                    <Loading className={styles.loadingIcon} color='#cdc6c1' />
                </View>
            );
        },
        [handleViewPic]
    );

    const successComponent = useCallback(
        item => {
            return (
                <WImage
                    onClick={() => handleViewPic(item?.path)}
                    className={styles.imageItem}
                    shape='rounded'
                    src={item?.path}
                />
            );
        },
        [handleViewPic]
    );

    const errorComponent = useCallback(
        (item, idx) => {
            return (
                <View key={idx} className={styles.errCom}>
                    <WImage className={styles.errImg} shape='rounded' src={item?.path} />
                    <View className={styles.coverView} />
                    <View
                        className={cx(styles.errItext, 'wz-flex')}
                        onClick={() => handleRetryUpload(item, idx)}
                    >
                        <WImage
                            className={styles.retryUpload}
                            src='https://med-fe.cdn.bcebos.com/vita/retry.png'
                        />
                        <Text className={cx(styles.retryText, 'wz-mt-30')}>重新上传</Text>
                    </View>
                </View>
            );
        },
        [handleRetryUpload]
    );

    const renderImg = useCallback(
        (item, idx) => {
            const {uploadStatus} = item || {};

            switch (uploadStatus) {
                case 'pending':
                    return loadComponent(item);
                case 'success':
                    return successComponent(item);
                case 'failed':
                    return errorComponent(item, idx);
                default:
                    return successComponent(item);
            }
        },
        [errorComponent, loadComponent, successComponent]
    );

    useEffect(()=>{
        const handleEvent = ({sceneType, btnType, count}) => {
            handleUpload(sceneType, btnType, count || 1);
        }

        eventCenter.on('handleUpload', handleEvent);
        return () => {
            eventCenter.off('handleUpload');
        };
    }, [handleUpload]);

    useEffect(()=>{
        if(isMultiple){
            if(imgList.length < 5){
                setShowAddImageIcon(true);
                ubcCommonViewSend({
                    value: 'continueUpload_show'
                })
            }else{
                setShowAddImageIcon(false);
            }
            
        }else{
            setShowAddImageIcon(false);
        }
    },[imgList, isMultiple])

    if (!imgList.length) return;
    return (
        <View className={cx(styles.imageMain, 'wz-flex', 'wz-mlr-36', 'wz-ptb-45', 'wz-plr-45')}>
            <View className={styles.capsuleShadow} />
            {imgList?.map((item, idx) => {
                return (
                    <View key={idx} className={cx(styles.imageContainer, 'wz-mr-36')}>
                        {renderImg(item, idx)}
                        <WImage
                            src={deleteImgIcon}
                            className={styles.closeIcon}
                            onClick={() => {
                                setImgList(imgList?.filter(it => it?.path !== item?.path));
                                setStatusList(statusList?.filter((_it, s) => s !== idx));
                            }}
                        />
                    </View>
                );
            })}
            {showAddImageIcon && (<View className={cx(styles.continueUploadMain, 'wz-flex')} onClick={()=>handleUpload('','',5)}>
                <WImage className={styles.continueUpload} src={continueUploadIocn}/>
            </View>) }
        </View>
    );
};

ImageListTools.displayName = 'ImageListTools';

export default ImageListTools;

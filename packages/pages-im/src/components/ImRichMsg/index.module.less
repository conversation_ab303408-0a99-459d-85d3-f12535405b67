.bubbleWrapper {
    flex-direction: column;
    align-items: flex-end;
    border-radius: 48px;
    box-shadow: 0 0 200.01px 0 #00000003;
}

.bubbleWrapperImages {
    background-color: #fff;
    border-top-right-radius: 12px;
    border-top-left-radius: 60px;
    flex-wrap: wrap;
}

.bubbleWrapperSystem {
    align-items: flex-start;
}

.imageWrapper {
    flex-wrap: wrap;

    .imageBorder {
        border: 1px solid gray;
        border-radius: 20px;
    }
}

.textWrapper {
    display: inline-block;
}

.imRichMsg {
    &Private {
        width: 100%;
        height: 102px;
        color: #858585;
        box-sizing: border-box;
        background-color: #ebeced;
        border-bottom-left-radius: 48px;
        border-bottom-right-radius: 48px;

        &WaterMark {
            display: block;
            width: 36px;
            height: 36px;
            margin-right: 3px;
        }

        &Text {
            height: 36px;
            line-height: 39px;
            font-size: 36px;
            font-weight: 700;
        }
    }
}

.imgPrivate {
    width: 100%;
    justify-content: center;
    background-color: #f4f6ff;
    border-bottom-right-radius: 60px;
    border-bottom-left-radius: 60px;
    font-size: 36px;
    color: #858585;

    .imgWaterMark {
        width: 36px;
        height: 36px;
    }
}

.imgItem {
    margin: 0 24px 24px 0;
    position: relative;
    width: 228px;
    height: 228px;

    .imgSingleItem{
        width: 225px;
        height: 225px;
        border-radius: 45px;
    }

    .notBeResolved{
        position: absolute;
        top: 0;
        left: 0;
        color: #fff;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
        text-align: center;
        flex-direction: column;
        justify-content: center;
        border-radius: 30px;

        .attentionIcon{
            width: 54px;
            height: 54px;
        }
    }
}
.systemMsg{
    width: 100%;
    justify-content: center;

    .systemNotice{
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 42px;
        line-height: 42px;
        letter-spacing: 0px;
        text-align: center;
        color: #848691;
        background-color: rgba(0,0,0,.03);
        border-radius: 27px;
    }
}

